{"name": "docgen", "version": "1.0.0", "description": "DocGen streamlines your workflow by making document creation fast, flexible, and reliable. Design beautiful templates, generate custom documents in real time, and integrate with your existing systems using powerful APIs.", "main": "index.html", "scripts": {"dev": "npx serve .", "start": "npx serve .", "preview": "npx serve ."}, "keywords": ["document-generation", "templates", "api", "saas", "workflow", "automation", "documents", "framer"], "author": "Aziz <PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/aziz-souabni/DocGen.git"}, "homepage": "https://docgen.vercel.app", "engines": {"node": ">=14.0.0"}, "devDependencies": {"serve": "^14.2.1"}}