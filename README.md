# DocGen - UMO Editor Integration

A Vue.js application that integrates UMO Editor with DOCX file upload and editing capabilities.

## 🚀 Features

- **DOCX File Upload**: Drag-and-drop or click to upload DOCX files
- **Rich Text Editing**: Full-featured UMO Editor integration
- **Document Processing**: Convert DOCX to HTML using mammoth.js
- **Error Handling**: Comprehensive error handling and validation
- **Progress Tracking**: Real-time upload and processing progress
- **Export Options**: Export documents in various formats
- **Responsive Design**: Works on desktop and mobile devices

## 📋 Prerequisites

- Node.js 20.19.0 or higher
- npm or yarn package manager
- Modern web browser with ES6+ support

## 🛠️ Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Install UMO Editor and additional dependencies**:
   ```bash
   # Install UMO Editor
   npm install @umoteam/editor

   # Install DOCX processing library
   npm install mammoth

   # Install TypeScript types
   npm install @types/mammoth --save-dev
   ```

## 🏃‍♂️ Running the Application

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Open your browser** and navigate to `http://localhost:5173`

3. **Upload a DOCX file** and start editing!

## 📁 Project Structure

```
DocGen/
├── src/
│   ├── components/
│   │   ├── UmoDocumentEditor.vue    # Main editor component
│   │   └── FileUploadZone.vue       # File upload component
│   ├── utils/
│   │   └── documentProcessor.ts     # DOCX processing utilities
│   ├── tests/
│   │   └── DocumentProcessor.test.ts # Unit tests
│   ├── App.vue                      # Main application component
│   └── main.ts                      # Application entry point
├── package.json
├── SETUP_GUIDE.md                   # Detailed setup instructions
└── README.md                        # This file
```

## 🔧 Configuration

### UMO Editor Options

The editor can be configured in `UmoDocumentEditor.vue`:

```javascript
const editorOptions = {
  editorKey: 'docgen-editor',
  locale: 'en-US',
  theme: 'light', // or 'dark'
  height: '600px',
  toolbar: {
    defaultMode: 'ribbon',
    menus: ['base', 'insert', 'table', 'tools', 'page', 'export'],
    importWord: {
      enabled: true,
      maxSize: 1024 * 1024 * 10, // 10MB
      useCustomMethod: true
    }
  }
}
```

## 📖 Usage

### Basic Usage

1. **Upload a Document**:
   - Drag and drop a DOCX file onto the upload zone
   - Or click "Choose File" to browse and select a file

2. **Edit the Document**:
   - Use the rich text editor to modify content
   - Insert tables, images, and other elements
   - Apply formatting and styles

3. **Save and Export**:
   - Click "Save" to save changes
   - Click "Export" to download the document
   - Click "New Document" to start over

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm run test:unit

# Run tests in watch mode
npm run test:unit -- --watch

# Run tests with coverage
npm run test:unit -- --coverage
```

## 🔍 Troubleshooting

### Common Issues

1. **Package Installation Fails**:
   ```bash
   # Clear npm cache
   npm cache clean --force

   # Try with yarn
   yarn install
   ```

2. **DOCX Files Not Loading**:
   - Ensure the file is a valid DOCX format
   - Check file size (must be under 10MB by default)
   - Verify mammoth.js is properly installed

3. **Editor Not Rendering**:
   - Check browser console for errors
   - Ensure UMO Editor license is valid
   - Verify all dependencies are installed

## 📚 Documentation

- [UMO Editor Documentation](https://editor.umodoc.com/en/docs/)
- [Mammoth.js Documentation](https://github.com/mwilliamson/mammoth.js)
- [Vue.js Documentation](https://vuejs.org/guide/)
- [Detailed Setup Guide](./SETUP_GUIDE.md)

## 📄 License

This project is provided as-is for educational and development purposes.

### Required Licenses

- **UMO Editor**: Commercial license may be required for production use
- **Mammoth.js**: Apache License 2.0
- **Vue.js**: MIT License
