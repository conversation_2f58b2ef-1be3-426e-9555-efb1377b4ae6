# DocGen

DocGen streamlines your workflow by making document creation fast, flexible, and reliable. Design beautiful templates, generate custom documents in real time, and integrate with your existing systems using powerful APIs.

## 🚀 Features

- **Fast Document Generation**: Create custom documents in real-time
- **Beautiful Templates**: Design professional templates with ease
- **API Integration**: Seamlessly integrate with existing systems
- **Workflow Automation**: Streamline your document creation process
- **Real-time Notifications**: Webhook support for instant updates

## 🌐 Live Demo

Visit the live site: [https://docgen.vercel.app](https://docgen.vercel.app)

## 📁 Project Structure

```
DocGen/
├── index.html              # Main landing page
├── contact/
│   └── index.html          # Contact page
├── waitlist/
│   └── index.html          # Waitlist signup
├── blog/
│   ├── getting-started/
│   ├── building-trust/
│   ├── early-user-feedback/
│   └── landing-page-design/
├── vercel.json             # Vercel configuration
├── package.json            # Project metadata
└── README.md               # This file
```

## 🛠 Technology Stack

- **Frontend**: Static HTML/CSS/JavaScript
- **Framework**: Framer-generated website
- **Fonts**: Inter & Manrope
- **Hosting**: Vercel
- **CDN**: Framer CDN for assets and scripts

## 🚀 Deployment on Vercel

### Prerequisites

- Node.js 14+ installed
- Git repository
- Vercel account

### Quick Deploy

1. **Clone the repository**
   ```bash
   git clone https://github.com/aziz-souabni/DocGen.git
   cd DocGen
   ```

2. **Install Vercel CLI** (if not already installed)
   ```bash
   npm install -g vercel
   ```

3. **Deploy to Vercel**
   ```bash
   vercel
   ```

### Manual Deployment Steps

1. **Fork or clone this repository**

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your Git repository
   - Vercel will automatically detect it as a static site

3. **Configure deployment settings**
   - Build Command: Leave empty (static site)
   - Output Directory: Leave empty (root directory)
   - Install Command: `npm install` (optional)

4. **Deploy**
   - Click "Deploy"
   - Your site will be live in minutes!

### Environment Configuration

The project includes a `vercel.json` configuration file that handles:
- Clean URLs (removes .html extensions)
- Proper routing for all pages
- Security headers
- Cache optimization for static assets

## 🔧 Local Development

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start local server**
   ```bash
   npm run dev
   ```

3. **Open in browser**
   ```
   http://localhost:3000
   ```

## 📝 Content Management

### Blog Posts

The blog section includes articles about:
- Getting Started with SaaS
- Building Trust as an Early-Stage Brand
- Collecting User Feedback
- Landing Page Design

### Customization

To customize the content:
1. Edit the HTML files directly
2. Update meta tags for SEO
3. Modify the Framer-generated styles as needed

## 🔒 Security Features

- Content Security Policy headers
- XSS protection
- Frame options for clickjacking prevention
- Secure referrer policy

## 📊 Performance

- Optimized static assets
- CDN delivery via Framer
- Responsive design for all devices
- Fast loading times

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👤 Author

**Aziz SOUABNI**

## 🆘 Support

For support and questions:
- Visit the [Contact Page](https://docgen.vercel.app/contact)
- Open an issue on GitHub
- Join our [Waitlist](https://docgen.vercel.app/waitlist) for updates

---

Built with ❤️ using Framer and deployed on Vercel
