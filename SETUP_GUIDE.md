# UMO Editor Integration Guide for DocGen

This guide provides step-by-step instructions to integrate UMO Editor with DOCX file upload functionality into your Vue.js DocGen project.

## Prerequisites

- Node.js 20.19.0 or higher
- npm or yarn package manager
- Modern web browser with ES6+ support

## Step 1: Install Required Dependencies

Run the following commands in your project root:

```bash
# Install UMO Editor
npm install @umoteam/editor

# Install DOCX parsing library
npm install mammoth

# Install additional dependencies for file handling
npm install @types/mammoth --save-dev
```

## Step 2: Project Structure

Your project should have the following structure:

```
DocGen/
├── src/
│   ├── components/
│   │   └── UmoDocumentEditor.vue    # Main editor component
│   ├── App.vue                      # Updated main app component
│   └── main.ts                      # Vue app entry point
├── package.json
└── SETUP_GUIDE.md                   # This file
```

## Step 3: UMO Editor Configuration

The `UmoDocumentEditor.vue` component includes:

### Key Features:
- **File Upload Interface**: Drag-and-drop or click to upload DOCX files
- **DOCX Parsing**: Uses mammoth.js to convert DOCX to HTML
- **UMO Editor Integration**: Full-featured rich text editor
- **Error Handling**: Comprehensive error handling for file operations
- **Export Functionality**: Export documents in various formats

### Configuration Options:
```javascript
const editorOptions = {
  editorKey: 'docgen-editor',
  locale: 'en-US',
  theme: 'light',
  height: '600px',
  toolbar: {
    defaultMode: 'ribbon',
    menus: ['base', 'insert', 'table', 'tools', 'page', 'export'],
    importWord: {
      enabled: true,
      maxSize: 1024 * 1024 * 10, // 10MB limit
      useCustomMethod: true
    }
  }
}
```

## Step 4: File Upload and Processing

### Supported File Types:
- `.docx` (Microsoft Word 2007+)
- `.doc` (Microsoft Word 97-2003) - limited support

### File Size Limits:
- Maximum file size: 10MB
- Configurable in the component

### Processing Flow:
1. User uploads DOCX file via drag-and-drop or file picker
2. File validation (type and size)
3. DOCX parsing using mammoth.js
4. Content loading into UMO Editor
5. Ready for editing

## Step 5: Running the Application

1. **Install dependencies** (if not done already):
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open browser** and navigate to `http://localhost:5173`

## Step 6: Usage Instructions

### Uploading a Document:
1. Click "Choose File" or drag a DOCX file to the upload area
2. Wait for the document to process
3. The editor will load with your document content

### Editing Features:
- Full rich text editing capabilities
- Insert tables, images, and other elements
- Format text with various styles
- Page layout controls

### Saving and Exporting:
- **Save**: Saves current content (implement backend integration)
- **Export**: Downloads document as HTML (extend for other formats)
- **New Document**: Resets editor for new document

## Step 7: Customization Options

### Theme Customization:
```javascript
// In UmoDocumentEditor.vue
editorOptions.theme = 'dark' // or 'light'
```

### Toolbar Customization:
```javascript
// Customize available toolbar menus
editorOptions.toolbar.menus = ['base', 'insert', 'table']
```

### File Upload Limits:
```javascript
// Adjust file size limit (in bytes)
editorOptions.toolbar.importWord.maxSize = 1024 * 1024 * 20 // 20MB
```

## Step 8: Advanced Features

### Custom File Processing:
The component includes a `handleCustomImport` method that can be extended for:
- Custom DOCX parsing options
- Image handling and optimization
- Metadata extraction
- Content filtering

### Backend Integration:
Implement the `handleSave` method to:
- Save documents to your backend
- Implement version control
- Add collaboration features

### Export Options:
Extend the `exportDocument` method to support:
- PDF export
- DOCX export
- Markdown export
- Custom formats

## Troubleshooting

### Common Issues:

1. **Package Installation Fails**:
   - Check network connectivity
   - Try using yarn instead of npm
   - Clear npm cache: `npm cache clean --force`

2. **DOCX Files Not Loading**:
   - Ensure file is valid DOCX format
   - Check file size limits
   - Verify mammoth.js is properly installed

3. **Editor Not Rendering**:
   - Check browser console for errors
   - Ensure UMO Editor license is valid
   - Verify all dependencies are installed

4. **Styling Issues**:
   - Check CSS conflicts
   - Ensure proper component scoping
   - Verify responsive design breakpoints

### Browser Compatibility:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Next Steps

1. **Backend Integration**: Implement server-side document storage
2. **Authentication**: Add user authentication and document ownership
3. **Collaboration**: Implement real-time collaborative editing
4. **Version Control**: Add document versioning and history
5. **Advanced Export**: Implement PDF and DOCX export functionality

## Support and Documentation

- UMO Editor Documentation: https://editor.umodoc.com/en/docs/
- Mammoth.js Documentation: https://github.com/mwilliamson/mammoth.js
- Vue.js Documentation: https://vuejs.org/guide/

## License

This implementation is provided as-is for educational and development purposes. Ensure you have proper licenses for:
- UMO Editor (commercial license may be required)
- Any additional dependencies used in production
