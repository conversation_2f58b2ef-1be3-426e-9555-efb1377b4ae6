<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />

    <meta name="viewport" content="width=device-width" />
    <meta name="generator" content="Framer 18b201b" />
    <title>Contact Clario – Get in Touch</title>
    <meta
      name="description"
      content="Have a question or need help? Reach out to the Clario team and we’ll get back to you as soon as possible."
    />
    <meta
      name="framer-search-index"
      content="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/searchIndex-qKN37diWQqLf.json"
    />
    <meta
      name="framer-search-index-fallback"
      content="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/searchIndex-qzvljoTOcyQa.json"
    />
    <link
      href="https://framerusercontent.com/images/lwSkb6dL2iM31Eh2eKpNg5JQqSE.png"
      rel="icon"
      media="(prefers-color-scheme: light)"
    />
    <link
      href="https://framerusercontent.com/images/lwSkb6dL2iM31Eh2eKpNg5JQqSE.png"
      rel="icon"
      media="(prefers-color-scheme: dark)"
    />

    <meta property="og:type" content="website" />
    <meta property="og:title" content="Contact Clario – Get in Touch" />
    <meta
      property="og:description"
      content="Have a question or need help? Reach out to the Clario team and we’ll get back to you as soon as possible."
    />
    <meta
      property="og:image"
      content="https://framerusercontent.com/images/GBNAUcqSR82xaKWgtC0PqWmMs.png"
    />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Contact Clario – Get in Touch" />
    <meta
      name="twitter:description"
      content="Have a question or need help? Reach out to the Clario team and we’ll get back to you as soon as possible."
    />
    <meta
      name="twitter:image"
      content="https://framerusercontent.com/images/GBNAUcqSR82xaKWgtC0PqWmMs.png"
    />

    <meta name="robots" content="max-image-preview:large" />
    <style data-framer-breakpoint-css>
      @media (min-width: 1200px) {
        .hidden-qdhrgs {
          display: none !important;
        }
      }
      @media (min-width: 810px) and (max-width: 1199px) {
        .hidden-j0jgam {
          display: none !important;
        }
      }
      @media (max-width: 809px) {
        .hidden-1wemzx2 {
          display: none !important;
        }
      }
    </style>
    <style
      data-framer-css-ssr-minified
      data-framer-components="framer-lib-cursors-host framer-hSRtN PropertyOverrides framer-q36ud framer-sUZpT framer-lib-form-plain-text-input framer-QI2Hr"
    >
      html,
      body,
      #main {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      :root {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      * {
        box-sizing: border-box;
        -webkit-font-smoothing: inherit;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      p,
      figure {
        margin: 0;
      }
      body,
      input,
      textarea,
      select,
      button {
        font-size: 12px;
        font-family: sans-serif;
      }
      body {
        --token-2d3de992-80f6-43cc-b5d5-16857da63015: rgb(140, 255, 46);
        --token-743cf692-1243-473f-93be-c36de257addf: rgb(255, 255, 255);
        --token-a5fc4ef8-56b0-4925-86bf-ce9af08e7778: rgb(47, 47, 47);
        --token-142de566-1cef-4aec-a905-86f484066d50: rgb(13, 13, 13);
        --token-0bd9300c-1d9c-48e3-b47c-3d641fa8f8ff: rgb(5, 5, 5);
        --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a: rgba(255, 255, 255, 0.65);
        --token-b0e81180-dc84-49c8-98af-9bb3ddda4fb3: rgb(23, 23, 23);
        --token-a5f375a3-aff8-4b16-9dc5-1036bd292ad2: rgb(248, 248, 250);
      }
      @supports (z-index: calc(infinity)) {
        #__framer-badge-container {
          --infinity: infinity;
        }
      }
      #__framer-badge-container {
        position: fixed;
        bottom: 0;
        padding: 20px;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        pointer-events: none;
        z-index: calc(var(--infinity, 2147483647));
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/5vvr9Vy74if2I6bQbJvbw7SY1pQ.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF,
          U+A640-A69F, U+FE2E-FE2F;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/EOr0mi4hNtlgWNn9if640EZzXCo.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/Y9k9QrlZAqio88Klkmbd8VoMQc.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+1F00-1FFF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/OYrD2tBIBPvoJXiIHnLoOXnY9M.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0370-03FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/JeYwfuaPfZHQhEG8U5gtPDZ7WQ.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB,
          U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/GrgcKwrN6d3Uz8EwcLHZxwEfC4.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2070, U+2074-207E, U+2080-208E, U+20AC,
          U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/b6Y37FthZeALduNqHicBT6FutY.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
          U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/vQyevYAyHtARFwPqUzQGpnDs.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
          U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/DpPBYI0sL4fYLgAkX8KXOPVt7c.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF,
          U+A640-A69F, U+FE2E-FE2F;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/4RAEQdEOrcnDkhHiiCbJOw92Lk.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/1K3W8DizY3v4emK8Mb08YHxTbs.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+1F00-1FFF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/tUSCtfYVM1I1IchuyCwz9gDdQ.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0370-03FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB,
          U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/DXD0Q7LSl7HEvDzucnyLnGBHM.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
          U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/GIryZETIX4IFypco5pYZONKhJIo.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
          U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/H89BbHkbHDzlxZzxi8uPzTsp90.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF,
          U+A640-A69F, U+FE2E-FE2F;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/u6gJwDuwB143kpNK1T1MDKDWkMc.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/43sJ6MfOPh1LCJt46OvyDuSbA6o.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+1F00-1FFF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/wccHG0r4gBDAIRhfHiOlq6oEkqw.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0370-03FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/WZ367JPwf9bRW6LdTHN8rXgSjw.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB,
          U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/QxmhnWTzLtyjIiZcfaLIJ8EFBXU.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
          U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/2A4Xx7CngadFGlVV4xrO06OBHY.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 700;
        unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
          U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/CfMzU8w2e7tHgF4T4rATMPuWosA.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF,
          U+A640-A69F, U+FE2E-FE2F;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/867QObYax8ANsfX4TGEVU9YiCM.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/Oyn2ZbENFdnW7mt2Lzjk1h9Zb9k.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+1F00-1FFF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/cdAe8hgZ1cMyLu9g005pAW3xMo.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0370-03FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/DOfvtmE1UplCq161m6Hj8CSQYg.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB,
          U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/vFzuJY0c65av44uhEKB6vyjFMg.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193,
          U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/tKtBcDnBMevsEEJKdNGhhkLzYo.woff2);
        font-display: swap;
        font-style: italic;
        font-weight: 400;
        unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169,
          U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;
      }
      @font-face {
        font-family: Inter;
        src: url(https://framerusercontent.com/assets/VgYFWiwsAC5OYxAycRXXvhze58.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6,
          U+02DA, U+02DC, U+2000-206F, U+2070, U+2074-207E, U+2080-208E, U+20AC,
          U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
      }
      @font-face {
        font-family: Manrope;
        src: url(https://framerusercontent.com/third-party-assets/fontshare/wf/BNWG6MUI4RTC6WEND2VPDH4MHMIVU3XZ/R5YXY5FMVG6PXU36GNEEA24MIPMEPGSM/CIM4KQCLZSMMLWPVH25IDDSTY4ENPHEY.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 500;
      }
      @font-face {
        font-family: Manrope;
        src: url(https://framerusercontent.com/third-party-assets/fontshare/wf/6U2SGH566NSNERG6RGEV3DSNEK7DL2RF/JRDYRKMSAW2H35IWEQIPL67HAJQ35MG5/JNU3GNMUBPWW6V6JTED3S27XL5HN7NM5.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 600;
      }
      @font-face {
        font-family: Manrope;
        src: url(https://framerusercontent.com/third-party-assets/fontshare/wf/NGBUP45ES3F7RD5XGKPEDJ6QEPO4TMOK/EXDVWJ2EDDVVV65UENMX33EDDYBX6OF7/6P4FPMFQH7CCC7RZ4UU4NKSGJ2RLF7V5.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 700;
      }
      @font-face {
        font-family: Manrope;
        src: url(https://framerusercontent.com/third-party-assets/fontshare/wf/2TYFCBHUANEXS6QGR5EQDUNAFH6LSWM3/AYNOU3VEA4LRTDNKJQUFNVNUTYSGOUOP/UXO4O7K2G3HI3D2VKD7UXVJVJD26P4BQ.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 400;
      }
      @font-face {
        font-family: Manrope;
        src: url(https://framerusercontent.com/third-party-assets/fontshare/wf/7EWHG4AMROQSXDCQTDPGBVASATB7CED2/TJSQTK5FHJ2MYKML5IXF2G6YTGFJLTYL/K4ZMLVLHYIFVTTTWGVOTVGOFUUX7NVGI.woff2);
        font-display: swap;
        font-style: normal;
        font-weight: 800;
      }
      @font-face {
        font-family: Inter Placeholder;
        src: local("Arial");
        ascent-override: 89.79%;
        descent-override: 22.36%;
        line-gap-override: 0%;
        size-adjust: 107.89%;
      }
      @font-face {
        font-family: Manrope Placeholder;
        src: local("Arial");
        ascent-override: 102.74%;
        descent-override: 28.91%;
        line-gap-override: 0%;
        size-adjust: 103.76%;
      }
      body {
        --framer-will-change-override: none;
      }
      @supports (background: -webkit-named-image(i)) and
        (not (grid-template-rows: subgrid)) {
        body {
          --framer-will-change-override: transform;
        }
      }
      [data-framer-component-type] {
        position: absolute;
      }
      [data-framer-component-type="Text"] {
        cursor: inherit;
      }
      [data-framer-component-text-autosized] * {
        white-space: pre;
      }
      [data-framer-component-type="Text"] > * {
        text-align: var(--framer-text-alignment, start);
      }
      [data-framer-component-type="Text"] span span,
      [data-framer-component-type="Text"] p span,
      [data-framer-component-type="Text"] h1 span,
      [data-framer-component-type="Text"] h2 span,
      [data-framer-component-type="Text"] h3 span,
      [data-framer-component-type="Text"] h4 span,
      [data-framer-component-type="Text"] h5 span,
      [data-framer-component-type="Text"] h6 span {
        display: block;
      }
      [data-framer-component-type="Text"] span span span,
      [data-framer-component-type="Text"] p span span,
      [data-framer-component-type="Text"] h1 span span,
      [data-framer-component-type="Text"] h2 span span,
      [data-framer-component-type="Text"] h3 span span,
      [data-framer-component-type="Text"] h4 span span,
      [data-framer-component-type="Text"] h5 span span,
      [data-framer-component-type="Text"] h6 span span {
        display: unset;
      }
      [data-framer-component-type="Text"] div div span,
      [data-framer-component-type="Text"] a div span,
      [data-framer-component-type="Text"] span span span,
      [data-framer-component-type="Text"] p span span,
      [data-framer-component-type="Text"] h1 span span,
      [data-framer-component-type="Text"] h2 span span,
      [data-framer-component-type="Text"] h3 span span,
      [data-framer-component-type="Text"] h4 span span,
      [data-framer-component-type="Text"] h5 span span,
      [data-framer-component-type="Text"] h6 span span,
      [data-framer-component-type="Text"] a {
        font-family: var(--font-family);
        font-style: var(--font-style);
        font-weight: min(
          calc(var(--framer-font-weight-increase, 0) + var(--font-weight, 400)),
          900
        );
        color: var(--text-color);
        letter-spacing: var(--letter-spacing);
        font-size: var(--font-size);
        text-transform: var(--text-transform);
        --text-decoration: var(--framer-text-decoration-style, solid)
          var(--framer-text-decoration, none)
          var(--framer-text-decoration-color, currentcolor)
          var(--framer-text-decoration-thickness, auto);
        --text-decoration-skip-ink: var(--framer-text-decoration-skip-ink);
        --text-underline-offset: var(--framer-text-decoration-offset);
        line-height: var(--line-height);
      }
      [data-framer-component-type="Text"] div div span,
      [data-framer-component-type="Text"] a div span,
      [data-framer-component-type="Text"] span span span,
      [data-framer-component-type="Text"] p span span,
      [data-framer-component-type="Text"] h1 span span,
      [data-framer-component-type="Text"] h2 span span,
      [data-framer-component-type="Text"] h3 span span,
      [data-framer-component-type="Text"] h4 span span,
      [data-framer-component-type="Text"] h5 span span,
      [data-framer-component-type="Text"] h6 span span,
      [data-framer-component-type="Text"] a {
        --font-family: var(--framer-font-family);
        --font-style: var(--framer-font-style);
        --font-weight: var(--framer-font-weight);
        --text-color: var(--framer-text-color);
        --letter-spacing: var(--framer-letter-spacing);
        --font-size: var(--framer-font-size);
        --text-transform: var(--framer-text-transform);
        --text-decoration: var(--framer-text-decoration-style, solid)
          var(--framer-text-decoration, none)
          var(--framer-text-decoration-color, currentcolor)
          var(--framer-text-decoration-thickness, auto);
        --text-decoration-skip-ink: var(--framer-text-decoration-skip-ink);
        --text-underline-offset: var(--framer-text-decoration-offset);
        --line-height: var(--framer-line-height);
      }
      [data-framer-component-type="Text"] a,
      [data-framer-component-type="Text"] a div span,
      [data-framer-component-type="Text"] a span span span,
      [data-framer-component-type="Text"] a p span span,
      [data-framer-component-type="Text"] a h1 span span,
      [data-framer-component-type="Text"] a h2 span span,
      [data-framer-component-type="Text"] a h3 span span,
      [data-framer-component-type="Text"] a h4 span span,
      [data-framer-component-type="Text"] a h5 span span,
      [data-framer-component-type="Text"] a h6 span span {
        --font-family: var(
          --framer-link-font-family,
          var(--framer-font-family)
        );
        --font-style: var(--framer-link-font-style, var(--framer-font-style));
        --font-weight: var(
          --framer-link-font-weight,
          var(--framer-font-weight)
        );
        --text-color: var(--framer-link-text-color, var(--framer-text-color));
        --font-size: var(--framer-link-font-size, var(--framer-font-size));
        --text-transform: var(
          --framer-link-text-transform,
          var(--framer-text-transform)
        );
        --text-decoration: var(
            --framer-link-text-decoration-style,
            var(--framer-text-decoration-style, solid)
          )
          var(
            --framer-link-text-decoration,
            var(--framer-text-decoration, none)
          )
          var(
            --framer-link-text-decoration-color,
            var(--framer-text-decoration-color, currentcolor)
          )
          var(
            --framer-link-text-decoration-thickness,
            var(--framer-text-decoration-thickness, auto)
          );
        --text-decoration-skip-ink: var(
          --framer-link-text-decoration-skip-ink,
          var(--framer-text-decoration-skip-ink)
        );
        --text-underline-offset: var(
          --framer-link-text-decoration-offset,
          var(--framer-text-decoration-offset)
        );
      }
      [data-framer-component-type="Text"] a:hover,
      [data-framer-component-type="Text"] a div span:hover,
      [data-framer-component-type="Text"] a span span span:hover,
      [data-framer-component-type="Text"] a p span span:hover,
      [data-framer-component-type="Text"] a h1 span span:hover,
      [data-framer-component-type="Text"] a h2 span span:hover,
      [data-framer-component-type="Text"] a h3 span span:hover,
      [data-framer-component-type="Text"] a h4 span span:hover,
      [data-framer-component-type="Text"] a h5 span span:hover,
      [data-framer-component-type="Text"] a h6 span span:hover {
        --font-family: var(
          --framer-link-hover-font-family,
          var(--framer-link-font-family, var(--framer-font-family))
        );
        --font-style: var(
          --framer-link-hover-font-style,
          var(--framer-link-font-style, var(--framer-font-style))
        );
        --font-weight: var(
          --framer-link-hover-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight))
        );
        --text-color: var(
          --framer-link-hover-text-color,
          var(--framer-link-text-color, var(--framer-text-color))
        );
        --font-size: var(
          --framer-link-hover-font-size,
          var(--framer-link-font-size, var(--framer-font-size))
        );
        --text-transform: var(
          --framer-link-hover-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform))
        );
        --text-decoration: var(
            --framer-link-hover-text-decoration-style,
            var(
              --framer-link-text-decoration-style,
              var(--framer-text-decoration-style, solid)
            )
          )
          var(
            --framer-link-hover-text-decoration,
            var(
              --framer-link-text-decoration,
              var(--framer-text-decoration, none)
            )
          )
          var(
            --framer-link-hover-text-decoration-color,
            var(
              --framer-link-text-decoration-color,
              var(--framer-text-decoration-color, currentcolor)
            )
          )
          var(
            --framer-link-hover-text-decoration-thickness,
            var(
              --framer-link-text-decoration-thickness,
              var(--framer-text-decoration-thickness, auto)
            )
          );
        --text-decoration-skip-ink: var(
          --framer-link-hover-text-decoration-skip-ink,
          var(
            --framer-link-text-decoration-skip-ink,
            var(--framer-text-decoration-skip-ink)
          )
        );
        --text-underline-offset: var(
          --framer-link-hover-text-decoration-offset,
          var(
            --framer-link-text-decoration-offset,
            var(--framer-text-decoration-offset)
          )
        );
      }
      [data-framer-component-type="Text"].isCurrent a,
      [data-framer-component-type="Text"].isCurrent a div span,
      [data-framer-component-type="Text"].isCurrent a span span span,
      [data-framer-component-type="Text"].isCurrent a p span span,
      [data-framer-component-type="Text"].isCurrent a h1 span span,
      [data-framer-component-type="Text"].isCurrent a h2 span span,
      [data-framer-component-type="Text"].isCurrent a h3 span span,
      [data-framer-component-type="Text"].isCurrent a h4 span span,
      [data-framer-component-type="Text"].isCurrent a h5 span span,
      [data-framer-component-type="Text"].isCurrent a h6 span span {
        --font-family: var(
          --framer-link-current-font-family,
          var(--framer-link-font-family, var(--framer-font-family))
        );
        --font-style: var(
          --framer-link-current-font-style,
          var(--framer-link-font-style, var(--framer-font-style))
        );
        --font-weight: var(
          --framer-link-current-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight))
        );
        --text-color: var(
          --framer-link-current-text-color,
          var(--framer-link-text-color, var(--framer-text-color))
        );
        --font-size: var(
          --framer-link-current-font-size,
          var(--framer-link-font-size, var(--framer-font-size))
        );
        --text-transform: var(
          --framer-link-current-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform))
        );
        --text-decoration: var(
            --framer-link-current-text-decoration-style,
            var(
              --framer-link-text-decoration-style,
              var(--framer-text-decoration-style, solid)
            )
          )
          var(
            --framer-link-current-text-decoration,
            var(
              --framer-link-text-decoration,
              var(--framer-text-decoration, none)
            )
          )
          var(
            --framer-link-current-text-decoration-color,
            var(
              --framer-link-text-decoration-color,
              var(--framer-text-decoration-color, currentcolor)
            )
          )
          var(
            --framer-link-current-text-decoration-thickness,
            var(
              --framer-link-text-decoration-thickness,
              var(--framer-text-decoration-thickness, auto)
            )
          );
        --text-decoration-skip-ink: var(
          --framer-link-current-text-decoration-skip-ink,
          var(
            --framer-link-text-decoration-skip-ink,
            var(--framer-text-decoration-skip-ink)
          )
        );
        --text-underline-offset: var(
          --framer-link-current-text-decoration-offset,
          var(
            --framer-link-text-decoration-offset,
            var(--framer-text-decoration-offset)
          )
        );
      }
      p.framer-text,
      div.framer-text,
      figure.framer-text,
      h1.framer-text,
      h2.framer-text,
      h3.framer-text,
      h4.framer-text,
      h5.framer-text,
      h6.framer-text,
      ol.framer-text,
      ul.framer-text {
        margin: 0;
        padding: 0;
      }
      p.framer-text,
      div.framer-text,
      h1.framer-text,
      h2.framer-text,
      h3.framer-text,
      h4.framer-text,
      h5.framer-text,
      h6.framer-text,
      li.framer-text,
      ol.framer-text,
      ul.framer-text,
      mark.framer-text,
      span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-blockquote-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-blockquote-font-style,
          var(--framer-font-style, normal)
        );
        font-weight: var(
          --framer-blockquote-font-weight,
          var(--framer-font-weight, 400)
        );
        color: var(
          --framer-blockquote-text-color,
          var(--framer-text-color, #000)
        );
        font-size: calc(
          var(--framer-blockquote-font-size, var(--framer-font-size, 16px)) *
            var(--framer-font-size-scale, 1)
        );
        letter-spacing: var(
          --framer-blockquote-letter-spacing,
          var(--framer-letter-spacing, 0)
        );
        text-transform: var(
          --framer-blockquote-text-transform,
          var(--framer-text-transform, none)
        );
        text-decoration-line: var(
          --framer-blockquote-text-decoration,
          var(--framer-text-decoration, initial)
        );
        text-decoration-style: var(
          --framer-blockquote-text-decoration-style,
          var(--framer-text-decoration-style, initial)
        );
        text-decoration-color: var(
          --framer-blockquote-text-decoration-color,
          var(--framer-text-decoration-color, initial)
        );
        text-decoration-thickness: var(
          --framer-blockquote-text-decoration-thickness,
          var(--framer-text-decoration-thickness, initial)
        );
        text-decoration-skip-ink: var(
          --framer-blockquote-text-decoration-skip-ink,
          var(--framer-text-decoration-skip-ink, initial)
        );
        text-underline-offset: var(
          --framer-blockquote-text-decoration-offset,
          var(--framer-text-decoration-offset, initial)
        );
        line-height: var(
          --framer-blockquote-line-height,
          var(--framer-line-height, 1.2em)
        );
        text-align: var(
          --framer-blockquote-text-alignment,
          var(--framer-text-alignment, start)
        );
        -webkit-text-stroke-width: var(--framer-text-stroke-width, initial);
        -webkit-text-stroke-color: var(--framer-text-stroke-color, initial);
        -moz-font-feature-settings: var(
          --framer-font-open-type-features,
          initial
        );
        -webkit-font-feature-settings: var(
          --framer-font-open-type-features,
          initial
        );
        font-feature-settings: var(--framer-font-open-type-features, initial);
        font-variation-settings: var(--framer-font-variation-axes, normal);
        text-wrap: var(--framer-text-wrap-override, var(--framer-text-wrap));
      }
      mark.framer-text,
      p.framer-text,
      div.framer-text,
      h1.framer-text,
      h2.framer-text,
      h3.framer-text,
      h4.framer-text,
      h5.framer-text,
      h6.framer-text,
      li.framer-text,
      ol.framer-text,
      ul.framer-text {
        background-color: var(
          --framer-blockquote-text-background-color,
          var(--framer-text-background-color, initial)
        );
        border-radius: var(
          --framer-blockquote-text-background-radius,
          var(--framer-text-background-radius, initial)
        );
        padding: var(
          --framer-blockquote-text-background-padding,
          var(--framer-text-background-padding, initial)
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        p.framer-text,
        div.framer-text,
        h1.framer-text,
        h2.framer-text,
        h3.framer-text,
        h4.framer-text,
        h5.framer-text,
        h6.framer-text,
        li.framer-text,
        ol.framer-text,
        ul.framer-text,
        span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-blockquote-text-color-rgb,
            var(
              --framer-blockquote-text-color,
              var(--framer-text-color-rgb, var(--framer-text-color, #000))
            )
          );
          -webkit-text-stroke-color: var(
            --framer-text-stroke-color-rgb,
            var(--framer-text-stroke-color, initial)
          );
        }
        mark.framer-text {
          background-color: var(
            --framer-blockquote-text-background-color-rgb,
            var(
              --framer-blockquote-text-background-color,
              var(
                --framer-text-background-color-rgb,
                var(--framer-text-background-color, initial)
              )
            )
          );
        }
      }
      .framer-fit-text .framer-text {
        white-space: nowrap;
        white-space-collapse: preserve;
      }
      strong.framer-text {
        font-family: var(
          --framer-blockquote-font-family-bold,
          var(--framer-font-family-bold)
        );
        font-style: var(
          --framer-blockquote-font-style-bold,
          var(--framer-font-style-bold)
        );
        font-weight: var(
          --framer-blockquote-font-weight-bold,
          var(--framer-font-weight-bold, bolder)
        );
        font-variation-settings: var(
          --framer-blockquote-font-variation-axes-bold,
          var(--framer-font-variation-axes-bold)
        );
      }
      em.framer-text {
        font-family: var(
          --framer-blockquote-font-family-italic,
          var(--framer-font-family-italic)
        );
        font-style: var(
          --framer-blockquote-font-style-italic,
          var(--framer-font-style-italic, italic)
        );
        font-weight: var(
          --framer-blockquote-font-weight-italic,
          var(--framer-font-weight-italic)
        );
        font-variation-settings: var(
          --framer-blockquote-font-variation-axes-italic,
          var(--framer-font-variation-axes-italic)
        );
      }
      em.framer-text > strong.framer-text {
        font-family: var(
          --framer-blockquote-font-family-bold-italic,
          var(--framer-font-family-bold-italic)
        );
        font-style: var(
          --framer-blockquote-font-style-bold-italic,
          var(--framer-font-style-bold-italic, italic)
        );
        font-weight: var(
          --framer-blockquote-font-weight-bold-italic,
          var(--framer-font-weight-bold-italic, bolder)
        );
        font-variation-settings: var(
          --framer-blockquote-font-variation-axes-bold-italic,
          var(--framer-font-variation-axes-bold-italic)
        );
      }
      p.framer-text:not(:first-child),
      div.framer-text:not(:first-child),
      h1.framer-text:not(:first-child),
      h2.framer-text:not(:first-child),
      h3.framer-text:not(:first-child),
      h4.framer-text:not(:first-child),
      h5.framer-text:not(:first-child),
      h6.framer-text:not(:first-child),
      ol.framer-text:not(:first-child),
      ul.framer-text:not(:first-child),
      blockquote.framer-text:not(:first-child),
      table.framer-text:not(:first-child),
      figure.framer-text:not(:first-child),
      .framer-image.framer-text:not(:first-child) {
        margin-top: var(
          --framer-blockquote-paragraph-spacing,
          var(--framer-paragraph-spacing, 0)
        );
      }
      li.framer-text > ul.framer-text:nth-child(2),
      li.framer-text > ol.framer-text:nth-child(2) {
        margin-top: 0;
      }
      .framer-text[data-text-fill] {
        display: inline-block;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding: max(
          0em,
          calc(
            calc(
                1.3em -
                  var(
                    --framer-blockquote-line-height,
                    var(--framer-line-height, 1.3em)
                  )
              ) / 2
          )
        );
        margin: min(
          0em,
          calc(
            calc(
                1.3em -
                  var(
                    --framer-blockquote-line-height,
                    var(--framer-line-height, 1.3em)
                  )
              ) / -2
          )
        );
      }
      code.framer-text,
      code.framer-text span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-code-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-blockquote-font-style,
          var(--framer-code-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-blockquote-font-weight,
          var(--framer-code-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-blockquote-text-color,
          var(--framer-code-text-color, var(--framer-text-color, #000))
        );
        font-size: calc(
          var(--framer-blockquote-font-size, var(--framer-font-size, 16px)) *
            var(--framer-font-size-scale, 1)
        );
        letter-spacing: var(
          --framer-blockquote-letter-spacing,
          var(--framer-letter-spacing, 0)
        );
        line-height: var(
          --framer-blockquote-line-height,
          var(--framer-line-height, 1.2em)
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        code.framer-text,
        code.framer-text span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-blockquote-text-color-rgb,
            var(
              --framer-blockquote-text-color,
              var(
                --framer-code-text-color-rgb,
                var(
                  --framer-code-text-color,
                  var(--framer-text-color-rgb, var(--framer-text-color, #000))
                )
              )
            )
          );
        }
      }
      blockquote.framer-text {
        margin-block-start: initial;
        margin-block-end: initial;
        margin-inline-start: initial;
        margin-inline-end: initial;
        unicode-bidi: initial;
      }
      a.framer-text,
      a.framer-text span.framer-text:not([data-text-fill]),
      span.framer-text[data-nested-link],
      span.framer-text[data-nested-link]
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-blockquote-font-family,
          var(
            --framer-link-font-family,
            var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
          )
        );
        font-style: var(
          --framer-blockquote-font-style,
          var(--framer-link-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-blockquote-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-blockquote-text-color,
          var(--framer-link-text-color, var(--framer-text-color, #000))
        );
        font-size: calc(
          var(--framer-blockquote-font-size, var(--framer-font-size, 16px)) *
            var(--framer-font-size-scale, 1)
        );
        text-transform: var(
          --framer-blockquote-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform, none))
        );
        cursor: var(--framer-custom-cursors, pointer);
        background-color: var(--framer-link-text-background-color, initial);
        border-radius: var(--framer-link-text-background-radius, initial);
        padding: var(--framer-link-text-background-padding, initial);
      }
      a.framer-text,
      span.framer-text[data-nested-link] {
        text-decoration-line: var(
          --framer-blockquote-text-decoration,
          var(
            --framer-link-text-decoration,
            var(--framer-text-decoration, initial)
          )
        );
        text-decoration-style: var(
          --framer-blockquote-text-decoration-style,
          var(
            --framer-link-text-decoration-style,
            var(--framer-text-decoration-style, initial)
          )
        );
        text-decoration-color: var(
          --framer-blockquote-text-decoration-color,
          var(
            --framer-link-text-decoration-color,
            var(--framer-text-decoration-color, initial)
          )
        );
        text-decoration-thickness: var(
          --framer-blockquote-text-decoration-thickness,
          var(
            --framer-link-text-decoration-thickness,
            var(--framer-text-decoration-thickness, initial)
          )
        );
        text-decoration-skip-ink: var(
          --framer-blockquote-text-decoration-skip-ink,
          var(
            --framer-link-text-decoration-skip-ink,
            var(--framer-text-decoration-skip-ink, initial)
          )
        );
        text-underline-offset: var(
          --framer-blockquote-text-decoration-offset,
          var(
            --framer-link-text-decoration-offset,
            var(--framer-text-decoration-offset, initial)
          )
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        a.framer-text,
        a.framer-text span.framer-text:not([data-text-fill]),
        span.framer-text[data-nested-link],
        span.framer-text[data-nested-link]
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-blockquote-text-color-rgb,
            var(
              --framer-blockquote-text-color,
              var(
                --framer-link-text-color-rgb,
                var(
                  --framer-link-text-color,
                  var(--framer-text-color-rgb, var(--framer-text-color, #000))
                )
              )
            )
          );
          background-color: var(
            --framer-link-text-background-color-rgb,
            var(--framer-link-text-background-color, initial)
          );
        }
      }
      code.framer-text a.framer-text,
      code.framer-text a.framer-text span.framer-text:not([data-text-fill]),
      code.framer-text span.framer-text[data-nested-link],
      code.framer-text
        span.framer-text[data-nested-link]
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-code-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-blockquote-font-style,
          var(--framer-code-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-blockquote-font-weight,
          var(--framer-code-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-blockquote-text-color,
          var(
            --framer-link-text-color,
            var(--framer-code-text-color, var(--framer-text-color, #000))
          )
        );
        font-size: calc(
          var(--framer-blockquote-font-size, var(--framer-font-size, 16px)) *
            var(--framer-font-size-scale, 1)
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        code.framer-text a.framer-text,
        code.framer-text a.framer-text span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-nested-link],
        code.framer-text
          span.framer-text[data-nested-link]
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-blockquote-text-color-rgb,
            var(
              --framer-blockquote-text-color,
              var(
                --framer-link-text-color-rgb,
                var(
                  --framer-link-text-color,
                  var(
                    --framer-code-text-color-rgb,
                    var(
                      --framer-code-text-color,
                      var(
                        --framer-text-color-rgb,
                        var(--framer-text-color, #000)
                      )
                    )
                  )
                )
              )
            )
          );
        }
      }
      a.framer-text:hover,
      a.framer-text:hover span.framer-text:not([data-text-fill]),
      span.framer-text[data-nested-link]:hover,
      span.framer-text[data-nested-link]:hover
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-link-hover-font-family,
          var(
            --framer-blockquote-font-family,
            var(
              --framer-link-font-family,
              var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
            )
          )
        );
        font-style: var(
          --framer-link-hover-font-style,
          var(
            --framer-blockquote-font-style,
            var(--framer-link-font-style, var(--framer-font-style, normal))
          )
        );
        font-weight: var(
          --framer-link-hover-font-weight,
          var(
            --framer-blockquote-font-weight,
            var(--framer-link-font-weight, var(--framer-font-weight, 400))
          )
        );
        color: var(
          --framer-link-hover-text-color,
          var(
            --framer-blockquote-text-color,
            var(--framer-link-text-color, var(--framer-text-color, #000))
          )
        );
        font-size: calc(
          var(
              --framer-link-hover-font-size,
              var(--framer-blockquote-font-size, var(--framer-font-size, 16px))
            ) * var(--framer-font-size-scale, 1)
        );
        text-transform: var(
          --framer-link-hover-text-transform,
          var(
            --framer-blockquote-text-transform,
            var(
              --framer-link-text-transform,
              var(--framer-text-transform, none)
            )
          )
        );
        background-color: var(
          --framer-link-hover-text-background-color,
          var(
            --framer-link-text-background-color,
            var(--framer-text-background-color, initial)
          )
        );
        border-radius: var(
          --framer-link-hover-text-background-radius,
          var(
            --framer-link-text-background-radius,
            var(--framer-text-background-radius, initial)
          )
        );
        padding: var(
          --framer-link-hover-text-background-padding,
          var(
            --framer-link-text-background-padding,
            var(--framer-text-background-padding, initial)
          )
        );
      }
      a.framer-text:hover,
      span.framer-text[data-nested-link]:hover {
        text-decoration-line: var(
          --framer-link-hover-text-decoration,
          var(
            --framer-blockquote-text-decoration,
            var(
              --framer-link-text-decoration,
              var(--framer-text-decoration, initial)
            )
          )
        );
        text-decoration-style: var(
          --framer-link-hover-text-decoration-style,
          var(
            --framer-blockquote-text-decoration-style,
            var(
              --framer-link-text-decoration-style,
              var(--framer-text-decoration-style, initial)
            )
          )
        );
        text-decoration-color: var(
          --framer-link-hover-text-decoration-color,
          var(
            --framer-blockquote-text-decoration-color,
            var(
              --framer-link-text-decoration-color,
              var(--framer-text-decoration-color, initial)
            )
          )
        );
        text-decoration-thickness: var(
          --framer-link-hover-text-decoration-thickness,
          var(
            --framer-blockquote-text-decoration-thickness,
            var(
              --framer-link-text-decoration-thickness,
              var(--framer-text-decoration-thickness, initial)
            )
          )
        );
        text-decoration-skip-ink: var(
          --framer-link-hover-text-decoration-skip-ink,
          var(
            --framer-blockquote-text-decoration-skip-ink,
            var(
              --framer-link-text-decoration-skip-ink,
              var(--framer-text-decoration-skip-ink, initial)
            )
          )
        );
        text-underline-offset: var(
          --framer-link-hover-text-decoration-offset,
          var(
            --framer-blockquote-text-decoration-offset,
            var(
              --framer-link-text-decoration-offset,
              var(--framer-text-decoration-offset, initial)
            )
          )
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        a.framer-text:hover,
        a.framer-text:hover span.framer-text:not([data-text-fill]),
        span.framer-text[data-nested-link]:hover,
        span.framer-text[data-nested-link]:hover
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-hover-text-color-rgb,
            var(
              --framer-link-hover-text-color,
              var(
                --framer-blockquote-text-color-rgb,
                var(
                  --framer-blockquote-text-color,
                  var(
                    --framer-link-text-color-rgb,
                    var(
                      --framer-link-text-color,
                      var(
                        --framer-text-color-rgb,
                        var(--framer-text-color, #000)
                      )
                    )
                  )
                )
              )
            )
          );
          background-color: var(
            --framer-link-hover-text-background-color-rgb,
            var(
              --framer-link-hover-text-background-color,
              var(
                --framer-link-text-background-color-rgb,
                var(
                  --framer-link-text-background-color,
                  var(
                    --framer-text-background-color-rgb,
                    var(--framer-text-background-color, initial)
                  )
                )
              )
            )
          );
        }
      }
      code.framer-text a.framer-text:hover,
      code.framer-text
        a.framer-text:hover
        span.framer-text:not([data-text-fill]),
      code.framer-text span.framer-text[data-nested-link]:hover,
      code.framer-text
        span.framer-text[data-nested-link]:hover
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          var(
            --framer-code-font-family,
            var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
          )
        );
        font-style: var(
          --framer-blockquote-font-style,
          var(--framer-code-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-blockquote-font-weight,
          var(--framer-code-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-link-hover-text-color,
          var(
            --framer-blockquote-text-color,
            var(
              --framer-link-text-color,
              var(--framer-code-text-color, var(--framer-text-color, #000))
            )
          )
        );
        font-size: calc(
          var(
              --framer-link-hover-font-size,
              var(
                --framer-blockquote-font-size,
                var(--framer-link-font-size, var(--framer-font-size, 16px))
              )
            ) * var(--framer-font-size-scale, 1)
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        code.framer-text a.framer-text:hover,
        code.framer-text
          a.framer-text:hover
          span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-nested-link]:hover,
        code.framer-text
          span.framer-text[data-nested-link]:hover
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-hover-text-color-rgb,
            var(
              --framer-link-hover-text-color,
              var(
                --framer-blockquote-text-color-rgb,
                var(
                  --framer-blockquote-text-color,
                  var(
                    --framer-link-text-color-rgb,
                    var(
                      --framer-link-text-color,
                      var(
                        --framer-text-color-rgb,
                        var(--framer-text-color, #000)
                      )
                    )
                  )
                )
              )
            )
          );
        }
      }
      a.framer-text[data-framer-page-link-current],
      a.framer-text[data-framer-page-link-current]
        span.framer-text:not([data-text-fill]),
      span.framer-text[data-framer-page-link-current],
      span.framer-text[data-framer-page-link-current]
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-link-current-font-family,
          var(
            --framer-link-font-family,
            var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
          )
        );
        font-style: var(
          --framer-link-current-font-style,
          var(--framer-link-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-link-current-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-link-current-text-color,
          var(--framer-link-text-color, var(--framer-text-color, #000))
        );
        font-size: calc(
          var(
              --framer-link-current-font-size,
              var(--framer-link-font-size, var(--framer-font-size, 16px))
            ) * var(--framer-font-size-scale, 1)
        );
        text-transform: var(
          --framer-link-current-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform, none))
        );
        background-color: var(
          --framer-link-current-text-background-color,
          var(--framer-link-text-background-color, initial)
        );
        border-radius: var(
          --framer-link-current-text-background-radius,
          var(--framer-link-text-background-radius, initial)
        );
        padding: var(
          --framer-link-current-text-background-padding,
          var(--framer-link-text-background-padding, initial)
        );
      }
      a.framer-text[data-framer-page-link-current],
      span.framer-text[data-framer-page-link-current] {
        text-decoration-line: var(
          --framer-link-current-text-decoration,
          var(
            --framer-link-text-decoration,
            var(--framer-text-decoration, initial)
          )
        );
        text-decoration-style: var(
          --framer-link-current-text-decoration-style,
          var(
            --framer-link-text-decoration-style,
            var(--framer-text-decoration-style, initial)
          )
        );
        text-decoration-color: var(
          --framer-link-current-text-decoration-color,
          var(
            --framer-link-text-decoration-color,
            var(--framer-text-decoration-color, initial)
          )
        );
        text-decoration-thickness: var(
          --framer-link-current-text-decoration-thickness,
          var(
            --framer-link-text-decoration-thickness,
            var(--framer-text-decoration-thickness, initial)
          )
        );
        text-decoration-skip-ink: var(
          --framer-link-current-text-decoration-skip-ink,
          var(
            --framer-link-text-decoration-skip-ink,
            var(--framer-text-decoration-skip-ink, initial)
          )
        );
        text-underline-offset: var(
          --framer-link-current-text-decoration-offset,
          var(
            --framer-link-text-decoration-offset,
            var(--framer-text-decoration-offset, initial)
          )
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        a.framer-text[data-framer-page-link-current],
        a.framer-text[data-framer-page-link-current]
          span.framer-text:not([data-text-fill]),
        span.framer-text[data-framer-page-link-current],
        span.framer-text[data-framer-page-link-current]
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-current-text-color-rgb,
            var(
              --framer-link-current-text-color,
              var(
                --framer-link-text-color-rgb,
                var(
                  --framer-link-text-color,
                  var(--framer-text-color-rgb, var(--framer-text-color, #000))
                )
              )
            )
          );
          background-color: var(
            --framer-link-current-text-background-color-rgb,
            var(
              --framer-link-current-text-background-color,
              var(
                --framer-link-text-background-color-rgb,
                var(
                  --framer-link-text-background-color,
                  var(
                    --framer-text-background-color-rgb,
                    var(--framer-text-background-color, initial)
                  )
                )
              )
            )
          );
        }
      }
      code.framer-text a.framer-text[data-framer-page-link-current],
      code.framer-text
        a.framer-text[data-framer-page-link-current]
        span.framer-text:not([data-text-fill]),
      code.framer-text span.framer-text[data-framer-page-link-current],
      code.framer-text
        span.framer-text[data-framer-page-link-current]
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-code-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-code-font-style,
          var(--framer-font-style, normal)
        );
        font-weight: var(
          --framer-code-font-weight,
          var(--framer-font-weight, 400)
        );
        color: var(
          --framer-link-current-text-color,
          var(
            --framer-link-text-color,
            var(--framer-code-text-color, var(--framer-text-color, #000))
          )
        );
        font-size: calc(
          var(
              --framer-link-current-font-size,
              var(--framer-link-font-size, var(--framer-font-size, 16px))
            ) * var(--framer-font-size-scale, 1)
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        code.framer-text a.framer-text[data-framer-page-link-current],
        code.framer-text
          a.framer-text[data-framer-page-link-current]
          span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-framer-page-link-current],
        code.framer-text
          span.framer-text[data-framer-page-link-current]
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-current-text-color-rgb,
            var(
              --framer-link-current-text-color,
              var(
                --framer-link-text-color-rgb,
                var(
                  --framer-link-text-color,
                  var(
                    --framer-code-text-color-rgb,
                    var(
                      --framer-code-text-color,
                      var(
                        --framer-text-color-rgb,
                        var(--framer-text-color, #000)
                      )
                    )
                  )
                )
              )
            )
          );
          background-color: var(
            --framer-link-current-text-background-color-rgb,
            var(
              --framer-link-current-text-background-color,
              var(
                --framer-link-text-background-color-rgb,
                var(
                  --framer-link-text-background-color,
                  var(
                    --framer-text-background-color-rgb,
                    var(--framer-text-background-color, initial)
                  )
                )
              )
            )
          );
        }
      }
      a.framer-text[data-framer-page-link-current]:hover,
      a.framer-text[data-framer-page-link-current]:hover
        span.framer-text:not([data-text-fill]),
      span.framer-text[data-framer-page-link-current]:hover,
      span.framer-text[data-framer-page-link-current]:hover
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-link-hover-font-family,
          var(
            --framer-link-current-font-family,
            var(
              --framer-link-font-family,
              var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
            )
          )
        );
        font-style: var(
          --framer-link-hover-font-style,
          var(
            --framer-link-current-font-style,
            var(--framer-link-font-style, var(--framer-font-style, normal))
          )
        );
        font-weight: var(
          --framer-link-hover-font-weight,
          var(
            --framer-link-current-font-weight,
            var(--framer-link-font-weight, var(--framer-font-weight, 400))
          )
        );
        color: var(
          --framer-link-hover-text-color,
          var(
            --framer-link-current-text-color,
            var(--framer-link-text-color, var(--framer-text-color, #000))
          )
        );
        font-size: calc(
          var(
              --framer-link-hover-font-size,
              var(
                --framer-link-current-font-size,
                var(--framer-link-font-size, var(--framer-font-size, 16px))
              )
            ) * var(--framer-font-size-scale, 1)
        );
        text-transform: var(
          --framer-link-hover-text-transform,
          var(
            --framer-link-current-text-transform,
            var(
              --framer-link-text-transform,
              var(--framer-text-transform, none)
            )
          )
        );
        background-color: var(
          --framer-link-hover-text-background-color,
          var(
            --framer-link-current-text-background-color,
            var(--framer-link-text-background-color, initial)
          )
        );
        border-radius: var(
          --framer-link-hover-text-background-radius,
          var(
            --framer-link-current-text-background-radius,
            var(--framer-link-text-background-radius, initial)
          )
        );
        padding: var(
          --framer-link-hover-text-background-padding,
          var(
            --framer-link-current-text-background-padding,
            var(--framer-link-text-background-padding, initial)
          )
        );
      }
      a.framer-text[data-framer-page-link-current]:hover,
      span.framer-text[data-framer-page-link-current]:hover {
        text-decoration-line: var(
          --framer-link-hover-text-decoration,
          var(
            --framer-link-current-text-decoration,
            var(
              --framer-link-text-decoration,
              var(--framer-text-decoration, initial)
            )
          )
        );
        text-decoration-style: var(
          --framer-link-hover-text-decoration-style,
          var(
            --framer-link-current-text-decoration-style,
            var(
              --framer-link-text-decoration-style,
              var(--framer-text-decoration-style, initial)
            )
          )
        );
        text-decoration-color: var(
          --framer-link-hover-text-decoration-color,
          var(
            --framer-link-current-text-decoration-color,
            var(
              --framer-link-text-decoration-color,
              var(--framer-text-decoration-color, initial)
            )
          )
        );
        text-decoration-thickness: var(
          --framer-link-hover-text-decoration-thickness,
          var(
            --framer-link-current-text-decoration-thickness,
            var(
              --framer-link-text-decoration-thickness,
              var(--framer-text-decoration-thickness, initial)
            )
          )
        );
        text-decoration-skip-ink: var(
          --framer-link-hover-text-decoration-skip-ink,
          var(
            --framer-link-current-text-decoration-skip-ink,
            var(
              --framer-link-text-decoration-skip-ink,
              var(--framer-text-decoration-skip-ink, initial)
            )
          )
        );
        text-underline-offset: var(
          --framer-link-hover-text-decoration-offset,
          var(
            --framer-link-current-text-decoration-offset,
            var(
              --framer-link-text-decoration-offset,
              var(--framer-text-decoration-offset, initial)
            )
          )
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        a.framer-text[data-framer-page-link-current]:hover,
        a.framer-text[data-framer-page-link-current]:hover
          span.framer-text:not([data-text-fill]),
        span.framer-text[data-framer-page-link-current]:hover,
        span.framer-text[data-framer-page-link-current]:hover
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-hover-text-color-rgb,
            var(
              --framer-link-hover-text-color,
              var(
                --framer-link-current-text-color-rgb,
                var(
                  --framer-link-current-text-color,
                  var(
                    --framer-link-text-color-rgb,
                    var(
                      --framer-link-text-color,
                      var(
                        --framer-code-text-color-rgb,
                        var(
                          --framer-code-text-color,
                          var(
                            --framer-text-color-rgb,
                            var(--framer-text-color, #000)
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
          );
          background-color: var(
            --framer-link-hover-text-background-color-rgb,
            var(
              --framer-link-hover-text-background-color,
              var(
                --framer-link-current-text-background-color-rgb,
                var(
                  --framer-link-current-text-background-color,
                  var(
                    --framer-link-text-background-color-rgb,
                    var(--framer-link-text-background-color, initial)
                  )
                )
              )
            )
          );
        }
      }
      code.framer-text a.framer-text[data-framer-page-link-current]:hover,
      code.framer-text
        a.framer-text[data-framer-page-link-current]:hover
        span.framer-text:not([data-text-fill]),
      code.framer-text span.framer-text[data-framer-page-link-current]:hover,
      code.framer-text
        span.framer-text[data-framer-page-link-current]:hover
        span.framer-text:not([data-text-fill]) {
        font-family: var(
          --framer-code-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-code-font-style,
          var(--framer-font-style, normal)
        );
        font-weight: var(
          --framer-code-font-weight,
          var(--framer-font-weight, 400)
        );
        color: var(
          --framer-link-hover-text-color,
          var(
            --framer-link-current-text-color,
            var(
              --framer-link-text-color,
              var(--framer-code-text-color, var(--framer-text-color, #000))
            )
          )
        );
        font-size: calc(
          var(
              --framer-link-hover-font-size,
              var(
                --framer-link-current-font-size,
                var(--framer-link-font-size, var(--framer-font-size, 16px))
              )
            ) * var(--framer-font-size-scale, 1)
        );
        background-color: var(
          --framer-link-hover-text-background-color,
          var(
            --framer-link-current-text-background-color,
            var(
              --framer-link-text-background-color,
              var(--framer-text-background-color, initial)
            )
          )
        );
        border-radius: var(
          --framer-link-hover-text-background-radius,
          var(
            --framer-link-current-text-background-radius,
            var(
              --framer-link-text-background-radius,
              var(--framer-text-background-radius, initial)
            )
          )
        );
        padding: var(
          --framer-link-hover-text-background-padding,
          var(
            --framer-link-current-text-background-padding,
            var(
              --framer-link-text-background-padding,
              var(--framer-text-background-padding, initial)
            )
          )
        );
      }
      @supports not (color: color(display-p3 1 1 1)) {
        code.framer-text a.framer-text[data-framer-page-link-current]:hover,
        code.framer-text
          a.framer-text[data-framer-page-link-current]:hover
          span.framer-text:not([data-text-fill]),
        code.framer-text span.framer-text[data-framer-page-link-current]:hover,
        code.framer-text
          span.framer-text[data-framer-page-link-current]:hover
          span.framer-text:not([data-text-fill]) {
          color: var(
            --framer-link-hover-text-color-rgb,
            var(
              --framer-link-hover-text-color,
              var(
                --framer-link-current-text-color-rgb,
                var(
                  --framer-link-current-text-color,
                  var(
                    --framer-link-text-color-rgb,
                    var(
                      --framer-link-text-color,
                      var(
                        --framer-code-text-color-rgb,
                        var(
                          --framer-code-text-color,
                          var(
                            --framer-text-color-rgb,
                            var(--framer-text-color, #000)
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
          );
          background-color: var(
            --framer-link-hover-text-background-color-rgb,
            var(
              --framer-link-hover-text-background-color,
              var(
                --framer-link-current-text-background-color-rgb,
                var(
                  --framer-link-current-text-background-color,
                  var(
                    --framer-link-text-background-color-rgb,
                    var(--framer-link-text-background-color, initial)
                  )
                )
              )
            )
          );
        }
      }
      .framer-image.framer-text {
        display: block;
        max-width: 100%;
        height: auto;
      }
      .text-styles-preset-reset.framer-text {
        --framer-font-family: Inter, Inter Placeholder, sans-serif;
        --framer-font-style: normal;
        --framer-font-weight: 500;
        --framer-text-color: #000;
        --framer-font-size: 16px;
        --framer-letter-spacing: 0;
        --framer-text-transform: none;
        --framer-text-decoration: none;
        --framer-text-decoration-style: none;
        --framer-text-decoration-color: none;
        --framer-text-decoration-thickness: none;
        --framer-text-decoration-skip-ink: none;
        --framer-text-decoration-offset: none;
        --framer-line-height: 1.2em;
        --framer-text-alignment: start;
        --framer-font-open-type-features: normal;
        --framer-text-background-color: initial;
        --framer-text-background-radius: initial;
        --framer-text-background-padding: initial;
      }
      ol.framer-text {
        --list-style-type: decimal;
      }
      ul.framer-text,
      ol.framer-text {
        padding-left: 3ch;
        position: relative;
      }
      li.framer-text {
        counter-increment: list-item;
        list-style: none;
      }
      ol.framer-text > li.framer-text:before {
        position: absolute;
        left: 0;
        content: counter(list-item, var(--list-style-type)) ".";
        font-variant-numeric: tabular-nums;
      }
      ol.framer-text > li.framer-text:nth-last-child(n + 100),
      ol.framer-text > li.framer-text:nth-last-child(n + 100) ~ li {
        padding-left: 1ch;
      }
      ol.framer-text > li.framer-text:nth-last-child(n + 1000),
      ol.framer-text > li.framer-text:nth-last-child(n + 1000) ~ li {
        padding-left: 2ch;
      }
      ol.framer-text > li.framer-text:nth-last-child(n + 10000),
      ol.framer-text > li.framer-text:nth-last-child(n + 10000) ~ li {
        padding-left: 3ch;
      }
      ol.framer-text > li.framer-text:nth-last-child(n + 100000),
      ol.framer-text > li.framer-text:nth-last-child(n + 100000) ~ li {
        padding-left: 4ch;
      }
      ol.framer-text > li.framer-text:nth-last-child(n + 1000000),
      ol.framer-text > li.framer-text:nth-last-child(n + 1000000) ~ li {
        padding-left: 5ch;
      }
      ul.framer-text > li.framer-text:before {
        position: absolute;
        left: 0;
        content: "\2022";
      }
      .framer-table-wrapper {
        overflow-x: auto;
      }
      table.framer-text,
      .framer-table-wrapper table.framer-text {
        border-collapse: separate;
        border-spacing: 0;
        table-layout: auto;
        word-break: normal;
        width: 100%;
      }
      td.framer-text,
      th.framer-text {
        min-width: 16ch;
        vertical-align: top;
      }
      .framer-text-module[style*="aspect-ratio"] > :first-child {
        width: 100%;
      }
      @supports not (aspect-ratio: 1) {
        .framer-text-module[style*="aspect-ratio"] {
          position: relative;
        }
      }
      @supports not (aspect-ratio: 1) {
        .framer-text-module[style*="aspect-ratio"]:before {
          content: "";
          display: block;
          padding-bottom: calc(100% / calc(var(--aspect-ratio)));
        }
      }
      @supports not (aspect-ratio: 1) {
        .framer-text-module[style*="aspect-ratio"] > :first-child {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
        }
      }
      [data-framer-component-type="DeprecatedRichText"] {
        cursor: inherit;
      }
      [data-framer-component-type="DeprecatedRichText"]
        .text-styles-preset-reset {
        --framer-font-family: Inter, Inter Placeholder, sans-serif;
        --framer-font-style: normal;
        --framer-font-weight: 500;
        --framer-text-color: #000;
        --framer-font-size: 16px;
        --framer-letter-spacing: 0;
        --framer-text-transform: none;
        --framer-text-decoration: none;
        --framer-line-height: 1.2em;
        --framer-text-alignment: start;
        --framer-font-open-type-features: normal;
        --font-variation-settings: normal;
      }
      [data-framer-component-type="DeprecatedRichText"] p,
      [data-framer-component-type="DeprecatedRichText"] div,
      [data-framer-component-type="DeprecatedRichText"] h1,
      [data-framer-component-type="DeprecatedRichText"] h2,
      [data-framer-component-type="DeprecatedRichText"] h3,
      [data-framer-component-type="DeprecatedRichText"] h4,
      [data-framer-component-type="DeprecatedRichText"] h5,
      [data-framer-component-type="DeprecatedRichText"] h6 {
        margin: 0;
        padding: 0;
      }
      [data-framer-component-type="DeprecatedRichText"] p,
      [data-framer-component-type="DeprecatedRichText"] div,
      [data-framer-component-type="DeprecatedRichText"] h1,
      [data-framer-component-type="DeprecatedRichText"] h2,
      [data-framer-component-type="DeprecatedRichText"] h3,
      [data-framer-component-type="DeprecatedRichText"] h4,
      [data-framer-component-type="DeprecatedRichText"] h5,
      [data-framer-component-type="DeprecatedRichText"] h6,
      [data-framer-component-type="DeprecatedRichText"] li,
      [data-framer-component-type="DeprecatedRichText"] ol,
      [data-framer-component-type="DeprecatedRichText"] ul,
      [data-framer-component-type="DeprecatedRichText"]
        span:not([data-text-fill]) {
        font-family: var(
          --framer-font-family,
          Inter,
          Inter Placeholder,
          sans-serif
        );
        font-style: var(--framer-font-style, normal);
        font-weight: var(--framer-font-weight, 400);
        color: var(--framer-text-color, #000);
        font-size: var(--framer-font-size, 16px);
        letter-spacing: var(--framer-letter-spacing, 0);
        text-transform: var(--framer-text-transform, none);
        text-decoration: var(--framer-text-decoration, none);
        line-height: var(--framer-line-height, 1.2em);
        text-align: var(--framer-text-alignment, start);
      }
      [data-framer-component-type="DeprecatedRichText"] p:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] div:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h1:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h2:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h3:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h4:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h5:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] h6:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] ol:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"] ul:not(:first-child),
      [data-framer-component-type="DeprecatedRichText"]
        .framer-image:not(:first-child) {
        margin-top: var(--framer-paragraph-spacing, 0);
      }
      [data-framer-component-type="DeprecatedRichText"] span[data-text-fill] {
        display: inline-block;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      [data-framer-component-type="DeprecatedRichText"] a,
      [data-framer-component-type="DeprecatedRichText"]
        a
        span:not([data-text-fill]) {
        font-family: var(
          --framer-link-font-family,
          var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
        );
        font-style: var(
          --framer-link-font-style,
          var(--framer-font-style, normal)
        );
        font-weight: var(
          --framer-link-font-weight,
          var(--framer-font-weight, 400)
        );
        color: var(--framer-link-text-color, var(--framer-text-color, #000));
        font-size: var(--framer-link-font-size, var(--framer-font-size, 16px));
        text-transform: var(
          --framer-link-text-transform,
          var(--framer-text-transform, none)
        );
        text-decoration: var(
          --framer-link-text-decoration,
          var(--framer-text-decoration, none)
        );
      }
      [data-framer-component-type="DeprecatedRichText"] a:hover,
      [data-framer-component-type="DeprecatedRichText"]
        a:hover
        span:not([data-text-fill]) {
        font-family: var(
          --framer-link-hover-font-family,
          var(
            --framer-link-font-family,
            var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
          )
        );
        font-style: var(
          --framer-link-hover-font-style,
          var(--framer-link-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-link-hover-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-link-hover-text-color,
          var(--framer-link-text-color, var(--framer-text-color, #000))
        );
        font-size: var(
          --framer-link-hover-font-size,
          var(--framer-link-font-size, var(--framer-font-size, 16px))
        );
        text-transform: var(
          --framer-link-hover-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform, none))
        );
        text-decoration: var(
          --framer-link-hover-text-decoration,
          var(
            --framer-link-text-decoration,
            var(--framer-text-decoration, none)
          )
        );
      }
      [data-framer-component-type="DeprecatedRichText"]
        a[data-framer-page-link-current],
      [data-framer-component-type="DeprecatedRichText"]
        a[data-framer-page-link-current]
        span:not([data-text-fill]):not([data-nested-link]) {
        font-family: var(
          --framer-link-current-font-family,
          var(
            --framer-link-font-family,
            var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
          )
        );
        font-style: var(
          --framer-link-current-font-style,
          var(--framer-link-font-style, var(--framer-font-style, normal))
        );
        font-weight: var(
          --framer-link-current-font-weight,
          var(--framer-link-font-weight, var(--framer-font-weight, 400))
        );
        color: var(
          --framer-link-current-text-color,
          var(--framer-link-text-color, var(--framer-text-color, #000))
        );
        font-size: var(
          --framer-link-current-font-size,
          var(--framer-link-font-size, var(--framer-font-size, 16px))
        );
        text-transform: var(
          --framer-link-current-text-transform,
          var(--framer-link-text-transform, var(--framer-text-transform, none))
        );
        text-decoration: var(
          --framer-link-current-text-decoration,
          var(
            --framer-link-text-decoration,
            var(--framer-text-decoration, none)
          )
        );
      }
      [data-framer-component-type="DeprecatedRichText"]
        a[data-framer-page-link-current]:hover,
      [data-framer-component-type="DeprecatedRichText"]
        a[data-framer-page-link-current]:hover
        span:not([data-text-fill]):not([data-nested-link]) {
        font-family: var(
          --framer-link-hover-font-family,
          var(
            --framer-link-current-font-family,
            var(
              --framer-link-font-family,
              var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
            )
          )
        );
        font-style: var(
          --framer-link-hover-font-style,
          var(
            --framer-link-current-font-style,
            var(--framer-link-font-style, var(--framer-font-style, normal))
          )
        );
        font-weight: var(
          --framer-link-hover-font-weight,
          var(
            --framer-link-current-font-weight,
            var(--framer-link-font-weight, var(--framer-font-weight, 400))
          )
        );
        color: var(
          --framer-link-hover-text-color,
          var(
            --framer-link-current-text-color,
            var(--framer-link-text-color, var(--framer-text-color, #000))
          )
        );
        font-size: var(
          --framer-link-hover-font-size,
          var(
            --framer-link-current-font-size,
            var(--framer-link-font-size, var(--framer-font-size, 16px))
          )
        );
        text-transform: var(
          --framer-link-hover-text-transform,
          var(
            --framer-link-current-text-transform,
            var(
              --framer-link-text-transform,
              var(--framer-text-transform, none)
            )
          )
        );
        text-decoration: var(
          --framer-link-hover-text-decoration,
          var(
            --framer-link-current-text-decoration,
            var(
              --framer-link-text-decoration,
              var(--framer-text-decoration, none)
            )
          )
        );
      }
      [data-framer-component-type="DeprecatedRichText"] strong {
        font-weight: bolder;
      }
      [data-framer-component-type="DeprecatedRichText"] em {
        font-style: italic;
      }
      [data-framer-component-type="DeprecatedRichText"] .framer-image {
        display: block;
        max-width: 100%;
        height: auto;
      }
      [data-framer-component-type="DeprecatedRichText"] ul,
      [data-framer-component-type="DeprecatedRichText"] ol {
        display: table;
        width: 100%;
        padding-left: 0;
        margin: 0;
      }
      [data-framer-component-type="DeprecatedRichText"] li {
        display: table-row;
        counter-increment: list-item;
        list-style: none;
      }
      [data-framer-component-type="DeprecatedRichText"] ol > li:before {
        display: table-cell;
        width: 2.25ch;
        box-sizing: border-box;
        padding-right: 0.75ch;
        content: counter(list-item) ".";
        white-space: nowrap;
      }
      [data-framer-component-type="DeprecatedRichText"] ul > li:before {
        display: table-cell;
        width: 2.25ch;
        box-sizing: border-box;
        padding-right: 0.75ch;
        content: "\2022";
      }
      :not([data-framer-generated]) > [data-framer-stack-content-wrapper] > *,
      :not([data-framer-generated])
        > [data-framer-stack-content-wrapper]
        > [data-framer-component-type],
      :not([data-framer-generated])
        > [data-framer-stack-content-wrapper]
        > [data-framer-legacy-stack-gap-enabled]
        > *,
      :not([data-framer-generated])
        > [data-framer-stack-content-wrapper]
        > [data-framer-legacy-stack-gap-enabled]
        > [data-framer-component-type] {
        position: relative;
      }
      .flexbox-gap-not-supported
        [data-framer-legacy-stack-gap-enabled="true"]
        > *,
      [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"] {
        margin-top: calc(var(--stack-gap-y) / 2);
        margin-bottom: calc(var(--stack-gap-y) / 2);
        margin-right: calc(var(--stack-gap-x) / 2);
        margin-left: calc(var(--stack-gap-x) / 2);
      }
      [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled="true"] {
        row-gap: var(--stack-native-row-gap);
        column-gap: var(--stack-native-column-gap);
      }
      .flexbox-gap-not-supported
        [data-framer-stack-content-wrapper][data-framer-stack-gap-enabled="true"] {
        row-gap: unset;
        column-gap: unset;
      }
      .flexbox-gap-not-supported
        [data-framer-stack-direction-reverse="false"]
        [data-framer-legacy-stack-gap-enabled="true"]
        > *:first-child,
      [data-framer-stack-direction-reverse="false"]
        [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
        > *:first-child,
      .flexbox-gap-not-supported
        [data-framer-stack-direction-reverse="true"]
        [data-framer-legacy-stack-gap-enabled="true"]
        > *:last-child,
      [data-framer-stack-direction-reverse="true"]
        [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
        > *:last-child {
        margin-top: 0;
        margin-left: 0;
      }
      .flexbox-gap-not-supported
        [data-framer-stack-direction-reverse="false"]
        [data-framer-legacy-stack-gap-enabled="true"]
        > *:last-child,
      [data-framer-stack-direction-reverse="false"]
        [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
        > *:last-child,
      .flexbox-gap-not-supported
        [data-framer-stack-direction-reverse="true"]
        [data-framer-legacy-stack-gap-enabled="true"]
        > *:first-child,
      [data-framer-stack-direction-reverse="true"]
        [data-framer-legacy-stack-gap-enabled="true"][data-framer-stack-flexbox-gap="false"]
        > *:first-child {
        margin-right: 0;
        margin-bottom: 0;
      }
      NavigationContainer
        [data-framer-component-type="NavigationContainer"]
        > *,
      [data-framer-component-type="NavigationContainer"]
        > [data-framer-component-type] {
        position: relative;
      }
      [data-framer-component-type="Scroll"]::-webkit-scrollbar {
        display: none;
      }
      [data-framer-component-type="ScrollContentWrapper"] > * {
        position: relative;
      }
      [data-framer-component-type="NativeScroll"] {
        -webkit-overflow-scrolling: touch;
      }
      [data-framer-component-type="NativeScroll"] > * {
        position: relative;
      }
      [data-framer-component-type="NativeScroll"].direction-both {
        overflow-x: auto;
        overflow-y: auto;
      }
      [data-framer-component-type="NativeScroll"].direction-vertical {
        overflow-x: hidden;
        overflow-y: auto;
      }
      [data-framer-component-type="NativeScroll"].direction-horizontal {
        overflow-x: auto;
        overflow-y: hidden;
      }
      [data-framer-component-type="NativeScroll"].direction-vertical > * {
        width: 100% !important;
      }
      [data-framer-component-type="NativeScroll"].direction-horizontal > * {
        height: 100% !important;
      }
      [data-framer-component-type="NativeScroll"].scrollbar-hidden::-webkit-scrollbar {
        display: none;
      }
      [data-framer-component-type="PageContentWrapper"] > *,
      [data-framer-component-type="PageContentWrapper"]
        > [data-framer-component-type] {
        position: relative;
      }
      [data-framer-component-type="DeviceComponent"].no-device > * {
        width: 100% !important;
        height: 100% !important;
      }
      [data-is-present="false"],
      [data-is-present="false"] * {
        pointer-events: none !important;
      }
      [data-framer-cursor="pointer"] {
        cursor: pointer;
      }
      [data-framer-cursor="grab"] {
        cursor: grab;
      }
      [data-framer-cursor="grab"]:active {
        cursor: grabbing;
      }
      [data-framer-component-type="Frame"] *,
      [data-framer-component-type="Stack"] * {
        pointer-events: auto;
      }
      [data-framer-generated] * {
        pointer-events: unset;
      }
      .svgContainer svg {
        display: block;
      }
      [data-reset="button"] {
        border-width: 0;
        padding: 0;
        background: none;
      }
      [data-hide-scrollbars="true"]::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }
      [data-hide-scrollbars="true"]::-webkit-scrollbar-thumb {
        background: transparent;
      }
      [data-hide-scrollbars="true"] {
        scrollbar-width: none;
      }
      @supports (not (overflow: clip)) {
        :root {
          --overflow-clip-fallback: hidden;
        }
      }
      .framer-cursor-none,
      .framer-cursor-none * {
        cursor: none !important;
      }
      .framer-pointer-events-none,
      .framer-pointer-events-none * {
        pointer-events: none !important;
      }
      .framer-hSRtN.framer-6yr23w,
      .framer-hSRtN .framer-6yr23w {
        display: block;
      }
      .framer-hSRtN.framer-qdhrgs {
        align-content: center;
        align-items: center;
        background-color: var(
          --token-0bd9300c-1d9c-48e3-b47c-3d641fa8f8ff,
          #050505
        );
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 0px;
        height: min-content;
        justify-content: flex-start;
        overflow: hidden;
        padding: 0;
        position: relative;
        width: 1200px;
      }
      .framer-hSRtN .framer-1n6epg7 {
        align-content: center;
        align-items: center;
        display: flex;
        flex: none;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 20px;
        height: min-content;
        justify-content: center;
        min-height: 100vh;
        overflow: hidden;
        padding: 160px 40px 100px;
        position: relative;
        width: 100%;
      }
      .framer-hSRtN .framer-a4zowk {
        align-content: center;
        align-items: center;
        display: flex;
        flex: 1 0 0px;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 40px;
        height: min-content;
        justify-content: flex-start;
        max-width: 1000px;
        overflow: visible;
        padding: 0;
        position: relative;
        width: 1px;
      }
      .framer-hSRtN .framer-8ktpbp {
        align-content: center;
        align-items: center;
        display: flex;
        flex: none;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 20px;
        height: min-content;
        justify-content: center;
        overflow: hidden;
        padding: 0;
        position: relative;
        width: 100%;
        z-index: 3;
      }
      .framer-hSRtN .framer-iniuqd-container {
        flex: none;
        height: auto;
        position: relative;
        width: auto;
      }
      .framer-hSRtN .framer-18vhk2m {
        --framer-link-text-color: #0099ff;
        --framer-link-text-decoration: underline;
        --selection-background-color: rgba(140, 255, 46, 0.15);
        --selection-color: var(
          --token-2d3de992-80f6-43cc-b5d5-16857da63015,
          #8cff2e
        );
        flex: none;
        height: auto;
        max-width: 600px;
        position: relative;
        white-space: pre-wrap;
        width: 100%;
        word-break: break-word;
        word-wrap: break-word;
      }
      .framer-hSRtN .framer-1sibk6y,
      .framer-hSRtN .framer-1p5hx3f {
        --framer-link-text-color: #0099ff;
        --framer-link-text-decoration: underline;
        flex: none;
        height: auto;
        max-width: 400px;
        position: relative;
        white-space: pre-wrap;
        width: 100%;
        word-break: break-word;
        word-wrap: break-word;
      }
      .framer-hSRtN .framer-1gqivqc {
        align-content: center;
        align-items: center;
        display: flex;
        flex: none;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 20px;
        height: min-content;
        justify-content: center;
        max-width: 540px;
        overflow: visible;
        padding: 0;
        position: relative;
        width: 100%;
      }
      .framer-hSRtN .framer-rf0grw {
        align-content: flex-start;
        align-items: flex-start;
        background-color: var(
          --token-142de566-1cef-4aec-a905-86f484066d50,
          #0d0d0d
        );
        border-radius: 20px;
        display: flex;
        flex: none;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 20px;
        height: min-content;
        justify-content: flex-start;
        max-width: 540px;
        overflow: visible;
        padding: 20px;
        position: relative;
        width: 100%;
        z-index: 2;
      }
      .framer-hSRtN .framer-rh1150 {
        align-content: center;
        align-items: center;
        display: flex;
        flex: none;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 10px;
        height: min-content;
        justify-content: center;
        overflow: visible;
        padding: 0;
        position: relative;
        width: 100%;
      }
      .framer-hSRtN .framer-zte96t {
        align-content: flex-start;
        align-items: flex-start;
        display: flex;
        flex: 2 0 0px;
        flex-direction: column;
        flex-wrap: nowrap;
        gap: 10px;
        height: min-content;
        justify-content: flex-start;
        padding: 0;
        position: relative;
        width: 1px;
      }
      .framer-hSRtN .framer-18iywql-container {
        flex: none;
        height: 50px;
        position: relative;
        width: 100%;
      }
      .framer-hSRtN .framer-nhgtnw-container {
        flex: 1 0 0px;
        height: auto;
        position: relative;
        width: 1px;
      }
      .framer-hSRtN .framer-axjy1m {
        align-content: center;
        align-items: center;
        display: flex;
        flex: none;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 12px;
        height: min-content;
        justify-content: flex-start;
        min-height: 32px;
        min-width: 32px;
        overflow: visible;
        padding: 0;
        position: relative;
        width: min-content;
      }
      .framer-VE8XF .framer-styles-preset-1s297ft:not(.rich-text-wrapper),
      .framer-VE8XF .framer-styles-preset-1s297ft.rich-text-wrapper h1 {
        --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-family-bold: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
          "cv04" on, "cv11" on;
        --framer-font-size: 64px;
        --framer-font-style: normal;
        --framer-font-style-bold: normal;
        --framer-font-variation-axes: normal;
        --framer-font-weight: 500;
        --framer-font-weight-bold: 700;
        --framer-letter-spacing: -0.04em;
        --framer-line-height: 1em;
        --framer-paragraph-spacing: 0px;
        --framer-text-alignment: start;
        --framer-text-color: var(
          --token-743cf692-1243-473f-93be-c36de257addf,
          #ffffff
        );
        --framer-text-decoration: none;
        --framer-text-stroke-color: initial;
        --framer-text-stroke-width: initial;
        --framer-text-transform: none;
      }
      @media (max-width: 1199px) and (min-width: 810px) {
        .framer-VE8XF .framer-styles-preset-1s297ft:not(.rich-text-wrapper),
        .framer-VE8XF .framer-styles-preset-1s297ft.rich-text-wrapper h1 {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-family-bold-italic: "Inter", "Inter Placeholder",
            sans-serif;
          --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 54px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-style-bold-italic: italic;
          --framer-font-style-italic: italic;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 500;
          --framer-font-weight-bold: 700;
          --framer-font-weight-bold-italic: 700;
          --framer-font-weight-italic: 400;
          --framer-letter-spacing: -0.04em;
          --framer-line-height: 1em;
          --framer-paragraph-spacing: 0px;
          --framer-text-alignment: start;
          --framer-text-color: var(
            --token-743cf692-1243-473f-93be-c36de257addf,
            #ffffff
          );
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      @media (max-width: 809px) and (min-width: 0px) {
        .framer-VE8XF .framer-styles-preset-1s297ft:not(.rich-text-wrapper),
        .framer-VE8XF .framer-styles-preset-1s297ft.rich-text-wrapper h1 {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-family-bold-italic: "Inter", "Inter Placeholder",
            sans-serif;
          --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 48px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-style-bold-italic: italic;
          --framer-font-style-italic: italic;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 500;
          --framer-font-weight-bold: 700;
          --framer-font-weight-bold-italic: 700;
          --framer-font-weight-italic: 400;
          --framer-letter-spacing: -0.04em;
          --framer-line-height: 1em;
          --framer-paragraph-spacing: 0px;
          --framer-text-alignment: start;
          --framer-text-color: var(
            --token-743cf692-1243-473f-93be-c36de257addf,
            #ffffff
          );
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      .framer-R82EX .framer-styles-preset-1kqs40m:not(.rich-text-wrapper),
      .framer-R82EX .framer-styles-preset-1kqs40m.rich-text-wrapper p {
        --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-family-bold: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
          "cv04" on, "cv11" on;
        --framer-font-size: 18px;
        --framer-font-style: normal;
        --framer-font-style-bold: normal;
        --framer-font-variation-axes: normal;
        --framer-font-weight: 400;
        --framer-font-weight-bold: 700;
        --framer-letter-spacing: 0em;
        --framer-line-height: 1.5em;
        --framer-paragraph-spacing: 20px;
        --framer-text-alignment: start;
        --framer-text-color: rgba(255, 255, 255, 0.65);
        --framer-text-decoration: none;
        --framer-text-stroke-color: initial;
        --framer-text-stroke-width: initial;
        --framer-text-transform: none;
      }
      @media (max-width: 1199px) and (min-width: 810px) {
        .framer-R82EX .framer-styles-preset-1kqs40m:not(.rich-text-wrapper),
        .framer-R82EX .framer-styles-preset-1kqs40m.rich-text-wrapper p {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-family-bold-italic: "Inter", "Inter Placeholder",
            sans-serif;
          --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 17px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-style-bold-italic: italic;
          --framer-font-style-italic: italic;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 400;
          --framer-font-weight-bold: 700;
          --framer-font-weight-bold-italic: 700;
          --framer-font-weight-italic: 400;
          --framer-letter-spacing: 0em;
          --framer-line-height: 1.5em;
          --framer-paragraph-spacing: 20px;
          --framer-text-alignment: start;
          --framer-text-color: rgba(255, 255, 255, 0.65);
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      @media (max-width: 809px) and (min-width: 0px) {
        .framer-R82EX .framer-styles-preset-1kqs40m:not(.rich-text-wrapper),
        .framer-R82EX .framer-styles-preset-1kqs40m.rich-text-wrapper p {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-family-bold-italic: "Inter", "Inter Placeholder",
            sans-serif;
          --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 16px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-style-bold-italic: italic;
          --framer-font-style-italic: italic;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 400;
          --framer-font-weight-bold: 700;
          --framer-font-weight-bold-italic: 700;
          --framer-font-weight-italic: 400;
          --framer-letter-spacing: 0em;
          --framer-line-height: 1.5em;
          --framer-paragraph-spacing: 20px;
          --framer-text-alignment: start;
          --framer-text-color: rgba(255, 255, 255, 0.65);
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      .framer-A31tg .framer-styles-preset-cb0nmb:not(.rich-text-wrapper),
      .framer-A31tg .framer-styles-preset-cb0nmb.rich-text-wrapper p {
        --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-family-bold: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
          "cv04" on, "cv11" on;
        --framer-font-size: 14px;
        --framer-font-style: normal;
        --framer-font-style-bold: normal;
        --framer-font-variation-axes: normal;
        --framer-font-weight: 400;
        --framer-font-weight-bold: 700;
        --framer-letter-spacing: 0em;
        --framer-line-height: 1.5em;
        --framer-paragraph-spacing: 20px;
        --framer-text-alignment: start;
        --framer-text-color: var(
          --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
          rgba(255, 255, 255, 0.55)
        );
        --framer-text-decoration: none;
        --framer-text-stroke-color: initial;
        --framer-text-stroke-width: initial;
        --framer-text-transform: none;
      }
      @media (max-width: 1199px) and (min-width: 810px) {
        .framer-A31tg .framer-styles-preset-cb0nmb:not(.rich-text-wrapper),
        .framer-A31tg .framer-styles-preset-cb0nmb.rich-text-wrapper p {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Manrope", "Manrope Placeholder",
            sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 13px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 400;
          --framer-font-weight-bold: 700;
          --framer-letter-spacing: 0em;
          --framer-line-height: 1.5em;
          --framer-paragraph-spacing: 20px;
          --framer-text-alignment: start;
          --framer-text-color: var(
            --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
            rgba(255, 255, 255, 0.55)
          );
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      @media (max-width: 809px) and (min-width: 0px) {
        .framer-A31tg .framer-styles-preset-cb0nmb:not(.rich-text-wrapper),
        .framer-A31tg .framer-styles-preset-cb0nmb.rich-text-wrapper p {
          --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
          --framer-font-family-bold: "Manrope", "Manrope Placeholder",
            sans-serif;
          --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
            "cv04" on, "cv11" on;
          --framer-font-size: 12px;
          --framer-font-style: normal;
          --framer-font-style-bold: normal;
          --framer-font-variation-axes: normal;
          --framer-font-weight: 400;
          --framer-font-weight-bold: 700;
          --framer-letter-spacing: 0em;
          --framer-line-height: 1.5em;
          --framer-paragraph-spacing: 20px;
          --framer-text-alignment: start;
          --framer-text-color: var(
            --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
            rgba(255, 255, 255, 0.55)
          );
          --framer-text-decoration: none;
          --framer-text-stroke-color: initial;
          --framer-text-stroke-width: initial;
          --framer-text-transform: none;
        }
      }
      .framer-hSRtN[data-selection="true"] * ::selection,
      .framer-hSRtN [data-selection="true"] * ::selection {
        color: var(--selection-color, none);
        background-color: var(--selection-background-color, none);
      }
      @media (min-width: 810px) and (max-width: 1199px) {
        .framer-hSRtN.framer-qdhrgs {
          width: 810px;
        }
        .framer-hSRtN .framer-1n6epg7 {
          padding: 140px 30px 80px;
        }
        .framer-hSRtN .framer-1sibk6y,
        .framer-hSRtN .framer-1p5hx3f {
          max-width: 450px;
        }
      }
      @media (max-width: 809px) {
        .framer-hSRtN.framer-qdhrgs {
          width: 390px;
        }
        .framer-hSRtN .framer-1n6epg7 {
          flex-direction: column;
          padding: 120px 20px 100px;
        }
        .framer-hSRtN .framer-a4zowk,
        .framer-hSRtN .framer-zte96t,
        .framer-hSRtN .framer-nhgtnw-container {
          flex: none;
          width: 100%;
        }
        .framer-hSRtN .framer-18vhk2m {
          max-width: 540px;
        }
        .framer-hSRtN .framer-rf0grw {
          padding: 40px 20px;
        }
        .framer-hSRtN .framer-rh1150 {
          flex-direction: column;
          gap: 20px;
        }
        .framer-hSRtN .framer-axjy1m {
          min-width: 164px;
        }
      }
      .framer-q36ud.framer-1w0j3b5,
      .framer-q36ud .framer-1w0j3b5 {
        display: block;
      }
      .framer-q36ud.framer-lohjoe {
        align-content: center;
        align-items: center;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 8px;
        height: min-content;
        justify-content: center;
        overflow: visible;
        padding: 5px 10px;
        position: relative;
        width: min-content;
      }
      .framer-q36ud .framer-1lnlboo-container {
        flex: none;
        height: 12px;
        position: relative;
        width: 12px;
      }
      .framer-q36ud .framer-8saufy {
        aspect-ratio: 1 / 1;
        flex: none;
        height: var(--framer-aspect-ratio-supported, 5px);
        position: relative;
        width: 5px;
      }
      .framer-q36ud .framer-1u3q6hy {
        -webkit-user-select: none;
        flex: none;
        height: auto;
        position: relative;
        user-select: none;
        white-space: pre;
        width: auto;
      }
      .framer-K8Bhh .framer-styles-preset-1jsfakf:not(.rich-text-wrapper),
      .framer-K8Bhh .framer-styles-preset-1jsfakf.rich-text-wrapper p {
        --framer-font-family: "Manrope", "Manrope Placeholder", sans-serif;
        --framer-font-family-bold: "Inter", "Inter Placeholder", sans-serif;
        --framer-font-family-bold-italic: "Inter", "Inter Placeholder",
          sans-serif;
        --framer-font-family-italic: "Inter", "Inter Placeholder", sans-serif;
        --framer-font-open-type-features: "blwf" on, "cv09" on, "cv03" on,
          "cv04" on, "cv11" on;
        --framer-font-size: 16px;
        --framer-font-style: normal;
        --framer-font-style-bold: normal;
        --framer-font-style-bold-italic: italic;
        --framer-font-style-italic: italic;
        --framer-font-variation-axes: normal;
        --framer-font-weight: 500;
        --framer-font-weight-bold: 700;
        --framer-font-weight-bold-italic: 700;
        --framer-font-weight-italic: 400;
        --framer-letter-spacing: -0.02em;
        --framer-line-height: 1.4em;
        --framer-paragraph-spacing: 20px;
        --framer-text-alignment: start;
        --framer-text-color: var(
          --token-743cf692-1243-473f-93be-c36de257addf,
          #ffffff
        );
        --framer-text-decoration: none;
        --framer-text-stroke-color: initial;
        --framer-text-stroke-width: initial;
        --framer-text-transform: none;
      }
      .framer-form-text-input textarea::-webkit-resizer {
        background: no-repeat
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="m1.5 8 7-7M9 5.5l-3 3" stroke="%23999" stroke-width="1.5" stroke-linecap="round"></path></svg>');
      }
      .framer-form-text-input .framer-form-input[type="date"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="rgb(153, 153, 153)" d="M3 5a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2H3Z" opacity=".3"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M3.25 5.25a2 2 0 0 1 2-2h5.5a2 2 0 0 1 2 2v5.5a2 2 0 0 1-2 2h-5.5a2 2 0 0 1-2-2ZM3 6.75h9.5"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-form-text-input .framer-form-input[type="time"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 1 1-11 0Z"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7.75 8.25v-3m0 3h2"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-form-text-input textarea::-webkit-resizer {
        background: no-repeat
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="m1.5 8 7-7M9 5.5l-3 3" stroke="%23999" stroke-width="1.5" stroke-linecap="round"></path></svg>');
      }
      .framer-form-text-input .framer-form-input[type="date"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="rgb(153, 153, 153)" d="M3 5a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2H3Z" opacity=".3"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M3.25 5.25a2 2 0 0 1 2-2h5.5a2 2 0 0 1 2 2v5.5a2 2 0 0 1-2 2h-5.5a2 2 0 0 1-2-2ZM3 6.75h9.5"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-form-text-input .framer-form-input[type="time"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 1 1-11 0Z"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7.75 8.25v-3m0 3h2"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-sUZpT.framer-13dytdy,
      .framer-sUZpT .framer-13dytdy {
        display: block;
      }
      .framer-sUZpT.framer-hx6qqh {
        align-content: center;
        align-items: center;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 10px;
        height: 51px;
        justify-content: center;
        overflow: visible;
        padding: 0;
        position: relative;
        width: 560px;
      }
      .framer-sUZpT .framer-siudrk {
        --framer-input-focused-border-color: var(
          --token-2d3de992-80f6-43cc-b5d5-16857da63015,
          #8cff2e
        );
        --framer-input-focused-border-style: solid;
        --framer-input-focused-border-width: 2px;
        --framer-input-focused-box-shadow: 0px 0px 10px 0px
          rgba(140, 255, 46, 0.3);
        --framer-input-focused-transition: all 0.3s
          cubic-bezier(0.44, 0, 0.56, 1) 0s;
        --framer-input-font-family: "Manrope";
        --framer-input-font-letter-spacing: 0em;
        --framer-input-font-line-height: 1.2em;
        --framer-input-font-size: 14px;
        --framer-input-font-text-alignment: left;
        --framer-input-font-weight: 400;
        --framer-input-padding: 17px;
        flex: 1 0 0px;
        height: 100%;
        position: relative;
        width: 1px;
      }
      .framer-form-input {
        padding: var(--framer-input-padding);
        background: transparent;
        font-family: var(--framer-input-font-family);
        font-weight: var(--framer-input-font-weight);
        font-size: var(--framer-input-font-size);
        font-style: var(--framer-input-font-style);
        color: var(--framer-input-font-color);
        font-feature-settings: var(--framer-input-font-open-type-features);
        border: none;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        width: 100%;
        height: var(--framer-input-wrapper-height, 100%);
        letter-spacing: var(--framer-input-font-letter-spacing);
        text-align: var(--framer-input-font-text-alignment);
        line-height: var(--framer-input-font-line-height);
      }
      .framer-form-input:focus-visible {
        outline: none;
      }
      .framer-form-input-wrapper:after {
        content: "";
        pointer-events: none;
        box-sizing: border-box;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-top-left-radius: var(--framer-input-border-radius-top-left);
        border-top-right-radius: var(--framer-input-border-radius-top-right);
        border-bottom-right-radius: var(
          --framer-input-border-radius-bottom-right
        );
        border-bottom-left-radius: var(
          --framer-input-border-radius-bottom-left
        );
        border-color: var(--framer-input-border-color);
        border-top-width: var(--framer-input-border-top-width);
        border-right-width: var(--framer-input-border-right-width);
        border-bottom-width: var(--framer-input-border-bottom-width);
        border-left-width: var(--framer-input-border-left-width);
        border-style: var(--framer-input-border-style);
        transition: var(--framer-input-focused-transition);
        transition-property: border-color, border-width, border-style,
          border-top-left-radius, border-top-right-radius,
          border-bottom-right-radius, border-bottom-left-radius;
      }
      .framer-form-input-wrapper {
        overflow: hidden;
      }
      .framer-form-input-wrapper {
        box-shadow: var(--framer-input-box-shadow);
        border-top-left-radius: var(--framer-input-border-radius-top-left);
        border-top-right-radius: var(--framer-input-border-radius-top-right);
        border-bottom-right-radius: var(
          --framer-input-border-radius-bottom-right
        );
        border-bottom-left-radius: var(
          --framer-input-border-radius-bottom-left
        );
        background: var(--framer-input-background);
        transition: var(--framer-input-focused-transition);
        transition-property: background, box-shadow;
      }
      .framer-form-text-input .framer-form-input::placeholder {
        color: var(--framer-input-placeholder-color);
      }
      .framer-form-text-input .framer-form-input[type="date"],
      .framer-form-text-input .framer-form-input[type="time"] {
        -webkit-appearance: none;
        appearance: none;
      }
      .framer-form-text-input .framer-form-input::-webkit-date-and-time-value {
        text-align: start;
      }
      .framer-form-text-input textarea {
        display: flex;
        resize: var(--framer-textarea-resize);
        overflow-y: auto;
        min-height: inherit;
        max-height: inherit;
        white-space: break-spaces;
      }
      .framer-form-text-input textarea::-webkit-resizer {
        background: no-repeat
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="m1.5 8 7-7M9 5.5l-3 3" stroke="%23999" stroke-width="1.5" stroke-linecap="round"></path></svg>');
      }
      .framer-form-text-input textarea::-webkit-scrollbar {
        cursor: pointer;
        background: transparent;
      }
      .framer-form-text-input
        textarea::-webkit-scrollbar-thumb:window-inactive {
        opacity: 0;
      }
      .framer-form-text-input textarea::-webkit-scrollbar-corner {
        background: none;
        background-color: transparent;
        outline: none;
      }
      .framer-form-text-input .framer-form-input::-webkit-datetime-edit {
        height: var(--framer-input-font-line-height);
      }
      .framer-form-text-input
        .framer-form-input.framer-form-input-empty::-webkit-datetime-edit {
        color: var(--framer-input-placeholder-color);
        -webkit-text-fill-color: var(--framer-input-placeholder-color);
        overflow: visible;
      }
      .framer-form-text-input .framer-form-input[type="date"]:before,
      .framer-form-text-input .framer-form-input[type="time"]:before {
        content: "";
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 16px;
        box-sizing: content-box;
        padding: var(--framer-input-padding);
        border: none;
        pointer-events: none;
        background-repeat: no-repeat;
        background-size: 16px;
        mask-repeat: no-repeat;
        mask-size: 16px;
        background-color: var(--framer-input-icon-color);
        padding-left: 10px;
        mask-position: 10px center;
        background-position: 10px center;
      }
      .framer-form-text-input .framer-form-input[type="date"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="rgb(153, 153, 153)" d="M3 5a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2H3Z" opacity=".3"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M3.25 5.25a2 2 0 0 1 2-2h5.5a2 2 0 0 1 2 2v5.5a2 2 0 0 1-2 2h-5.5a2 2 0 0 1-2-2ZM3 6.75h9.5"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-form-text-input .framer-form-input[type="time"]:before {
        mask-image: var(
          --framer-input-icon-mask-image,
          url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-width="1.5" d="M2.5 8a5.5 5.5 0 1 1 11 0 5.5 5.5 0 1 1-11 0Z"/><path fill="transparent" stroke="rgb(153, 153, 153)" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7.75 8.25v-3m0 3h2"/></svg>')
        );
        background-image: var(--framer-input-icon-image);
      }
      .framer-form-text-input
        .framer-form-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        padding: var(--framer-input-padding);
        padding-top: 0;
        padding-bottom: 0;
        width: 16px;
        height: 100%;
      }
      .framer-form-text-input:focus-within,
      .framer-form-text-input.framer-form-input-forced-focus {
        box-shadow: var(
          --framer-input-focused-box-shadow,
          var(--framer-input-box-shadow)
        );
        background: var(
          --framer-input-focused-background,
          var(--framer-input-background)
        );
      }
      .framer-form-text-input:focus-within:after,
      .framer-form-text-input.framer-form-input-forced-focus:after {
        border-color: var(
          --framer-input-focused-border-color,
          var(--framer-input-border-color)
        );
        border-style: var(
          --framer-input-focused-border-style,
          var(--framer-input-border-style)
        );
        border-width: var(
          --framer-input-focused-border-width,
          var(--framer-input-border-top-width)
            var(--framer-input-border-right-width)
            var(--framer-input-border-bottom-width)
            var(--framer-input-border-left-width)
        );
      }
      .ssr-variant {
        display: contents;
      }
      @supports (aspect-ratio: 1) {
        body {
          --framer-aspect-ratio-supported: auto;
        }
      }
      .framer-QI2Hr.framer-51q1q2,
      .framer-QI2Hr .framer-51q1q2 {
        display: block;
      }
      .framer-QI2Hr.framer-133ybg9 {
        align-content: center;
        align-items: center;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        gap: 10px;
        height: 50px;
        justify-content: center;
        overflow: visible;
        padding: 15px 20px;
        position: relative;
        width: min-content;
      }
      .framer-QI2Hr .framer-bf6m3t {
        -webkit-user-select: none;
        flex: none;
        height: auto;
        position: relative;
        user-select: none;
        white-space: pre;
        width: auto;
      }
      .framer-QI2Hr .framer-1mpz8gq {
        aspect-ratio: 1 / 1;
        flex: none;
        height: var(--framer-aspect-ratio-supported, 20px);
        overflow: hidden;
        position: relative;
        width: 20px;
      }
      .framer-QI2Hr .framer-wlb8ec {
        flex: none;
        inset: 0;
        overflow: visible;
        position: absolute;
      }
      .framer-QI2Hr .framer-1ciabnq {
        aspect-ratio: 1 / 1;
        flex: none;
        height: var(--framer-aspect-ratio-supported, 2px);
        left: 50%;
        overflow: visible;
        position: absolute;
        top: 0;
        width: 2px;
      }
      @supports (background: -webkit-named-image(i)) and
        (not (font-palette: dark)) {
        .framer-QI2Hr.framer-133ybg9 {
          gap: 0px;
        }
        .framer-QI2Hr.framer-133ybg9 > * {
          margin: 0 5px;
        }
        .framer-QI2Hr.framer-133ybg9 > :first-child {
          margin-left: 0;
        }
        .framer-QI2Hr.framer-133ybg9 > :last-child {
          margin-right: 0;
        }
      }
      .framer-QI2Hr.framer-v-1rtg4kl.framer-133ybg9,
      .framer-QI2Hr.framer-v-jp52vu.framer-133ybg9,
      .framer-QI2Hr.framer-v-hgrdhm.framer-133ybg9,
      .framer-QI2Hr.framer-v-4yxtdy.framer-133ybg9 {
        cursor: unset;
      }
      .framer-QI2Hr.framer-v-1rtg4kl .framer-wlb8ec {
        overflow: hidden;
      }
    </style>
  </head>
  <body>
    <script
      async
      src="https://events.framer.com/script?v=2"
      data-fid="db27b178285a48f53da68c3b32015625bc8690c04680c8add836ea232f208acf"
      data-no-nt
    ></script>

    <div
      id="main"
      data-framer-hydrate-v2='{"routeId":"iXqsMrUlo","localeId":"default","breakpoints":[{"hash":"qdhrgs","mediaQuery":"(min-width: 1200px)"},{"hash":"j0jgam","mediaQuery":"(min-width: 810px) and (max-width: 1199px)"},{"hash":"1wemzx2","mediaQuery":"(max-width: 809px)"}]}'
      data-framer-ssr-released-at="2025-08-13T16:44:23.700Z"
      data-framer-page-optimized-at="2025-08-19T02:29:45.315Z"
      data-framer-generated-page
    >
      <style data-framer-html-style>
        html body {
          background: var(
            --token-0bd9300c-1d9c-48e3-b47c-3d641fa8f8ff,
            rgb(5, 5, 5)
          );
        }
      </style>
      <div
        data-framer-root
        class="framer-hSRtN framer-VE8XF framer-R82EX framer-A31tg framer-qdhrgs"
        style="min-height: 100vh; width: auto"
      >
        <div class="framer-1n6epg7" data-framer-name="Heading">
          <div class="framer-a4zowk" data-framer-name="Container">
            <div class="framer-8ktpbp" data-framer-name="Headline">
              <div class="ssr-variant hidden-1wemzx2">
                <div class="framer-iniuqd-container">
                  <div
                    class="framer-q36ud framer-K8Bhh framer-lohjoe framer-v-lohjoe"
                    data-framer-name="Default"
                    style="
                      background-color: var(
                        --token-0bd9300c-1d9c-48e3-b47c-3d641fa8f8ff,
                        rgb(5, 5, 5)
                      );
                      border-bottom-left-radius: 10px;
                      border-bottom-right-radius: 10px;
                      border-top-left-radius: 10px;
                      border-top-right-radius: 10px;
                      box-shadow: inset 0px 1px 0px 0px rgba(140, 255, 47, 0.15),
                        inset 0px -1px 0px 0px rgba(140, 255, 47, 0.15),
                        0px 1px 2px 0px rgba(140, 255, 47, 0.4),
                        0px 3px 8px 0px rgba(140, 255, 47, 0.19),
                        0px 6px 4px 0px rgba(140, 255, 47, 0.05),
                        0px 11px 4px 0px rgba(140, 255, 47, 0.01),
                        0px 16px 5px 0px rgba(140, 255, 47, 0);
                    "
                  >
                    <div
                      class="framer-1u3q6hy"
                      style="
                        outline: none;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        flex-shrink: 0;
                        --extracted-r6o4lv: var(
                          --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                          rgb(140, 255, 46)
                        );
                        --framer-link-text-color: rgb(0, 153, 255);
                        --framer-link-text-decoration: underline;
                        transform: none;
                      "
                      data-framer-component-type="RichTextContainer"
                    >
                      <p
                        class="framer-text framer-styles-preset-1jsfakf"
                        data-styles-preset="VQGZB66Vz"
                        style="
                          --framer-text-color: var(
                            --extracted-r6o4lv,
                            var(
                              --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                              rgb(140, 255, 46)
                            )
                          );
                        "
                      >
                        Get Early Access
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="ssr-variant hidden-qdhrgs hidden-j0jgam">
                <div class="framer-iniuqd-container">
                  <div
                    class="framer-q36ud framer-K8Bhh framer-lohjoe framer-v-lohjoe"
                    data-framer-name="Default"
                    style="
                      background-color: var(
                        --token-0bd9300c-1d9c-48e3-b47c-3d641fa8f8ff,
                        rgb(5, 5, 5)
                      );
                      border-bottom-left-radius: 10px;
                      border-bottom-right-radius: 10px;
                      border-top-left-radius: 10px;
                      border-top-right-radius: 10px;
                      box-shadow: inset 0px 1px 0px 0px rgba(140, 255, 47, 0.15),
                        inset 0px -1px 0px 0px rgba(140, 255, 47, 0.15),
                        0px 1px 2px 0px rgba(140, 255, 47, 0.4),
                        0px 3px 8px 0px rgba(140, 255, 47, 0.19),
                        0px 6px 4px 0px rgba(140, 255, 47, 0.05),
                        0px 11px 4px 0px rgba(140, 255, 47, 0.01),
                        0px 16px 5px 0px rgba(140, 255, 47, 0);
                    "
                  >
                    <div
                      class="framer-1u3q6hy"
                      style="
                        outline: none;
                        display: flex;
                        flex-direction: column;
                        justify-content: flex-start;
                        flex-shrink: 0;
                        --extracted-r6o4lv: var(
                          --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                          rgb(140, 255, 46)
                        );
                        --framer-link-text-color: rgb(0, 153, 255);
                        --framer-link-text-decoration: underline;
                        transform: none;
                      "
                      data-framer-component-type="RichTextContainer"
                    >
                      <p
                        class="framer-text framer-styles-preset-1jsfakf"
                        data-styles-preset="VQGZB66Vz"
                        style="
                          --framer-text-color: var(
                            --extracted-r6o4lv,
                            var(
                              --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                              rgb(140, 255, 46)
                            )
                          );
                        "
                      >
                        Get Early Access
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="framer-18vhk2m"
                data-selection="true"
                style="
                  outline: none;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  flex-shrink: 0;
                  transform: none;
                "
                data-framer-component-type="RichTextContainer"
              >
                <h1
                  class="framer-text framer-styles-preset-1s297ft"
                  data-styles-preset="SqFjj1czL"
                  style="--framer-text-alignment: center"
                >
                  We’re getting<br class="framer-text" />ready to launch
                </h1>
              </div>
              <div
                class="framer-1sibk6y"
                style="
                  outline: none;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  flex-shrink: 0;
                  transform: none;
                "
                data-framer-component-type="RichTextContainer"
              >
                <p
                  class="framer-text framer-styles-preset-1kqs40m"
                  data-styles-preset="nqwdXorsW"
                  style="
                    --framer-text-alignment: center;
                    --framer-text-color: var(
                      --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
                      rgba(255, 255, 255, 0.55)
                    );
                  "
                >
                  Join our waitlist to get early access to DocGen’s next-gen
                  features, exclusive updates.
                </p>
              </div>
            </div>
            <div class="framer-1gqivqc" data-framer-name="Bottom">
              <form
                class="framer-rf0grw"
                style="
                  will-change: transform;
                  opacity: 0;
                  transform: translateY(40px);
                "
              >
                <div class="framer-rh1150" data-framer-name="Multiple">
                  <label class="framer-zte96t"
                    ><div class="ssr-variant hidden-1wemzx2 hidden-j0jgam">
                      <div class="framer-18iywql-container">
                        <button
                          class="framer-sUZpT framer-hx6qqh framer-v-hx6qqh"
                          data-framer-name="Variant 1"
                          data-reset="button"
                          style="height: 100%; width: 100%"
                        >
                          <div
                            style="
                              --framer-input-background: var(
                                --token-a5fc4ef8-56b0-4925-86bf-ce9af08e7778,
                                rgb(47, 47, 47)
                              );
                              --framer-input-border-radius-bottom-left: 10px;
                              --framer-input-border-radius-bottom-right: 10px;
                              --framer-input-border-radius-top-left: 10px;
                              --framer-input-border-radius-top-right: 10px;
                              --framer-input-font-color: var(
                                --token-743cf692-1243-473f-93be-c36de257addf,
                                rgb(255, 255, 255)
                              );
                              --framer-input-icon-mask-image: none;
                              --framer-input-placeholder-color: var(
                                --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
                                rgba(255, 255, 255, 0.65)
                              );
                            "
                            class="framer-form-text-input framer-form-input-wrapper framer-siudrk"
                          >
                            <input
                              type="email"
                              name="Email"
                              placeholder="Your email address"
                              class="framer-form-input framer-form-input-empty"
                            />
                          </div>
                        </button>
                      </div>
                    </div>
                    <div class="ssr-variant hidden-qdhrgs hidden-j0jgam">
                      <div class="framer-18iywql-container">
                        <button
                          class="framer-sUZpT framer-hx6qqh framer-v-hx6qqh"
                          data-framer-name="Variant 1"
                          data-reset="button"
                          style="height: 100%; width: 100%"
                        >
                          <div
                            style="
                              --framer-input-background: var(
                                --token-a5fc4ef8-56b0-4925-86bf-ce9af08e7778,
                                rgb(47, 47, 47)
                              );
                              --framer-input-border-radius-bottom-left: 10px;
                              --framer-input-border-radius-bottom-right: 10px;
                              --framer-input-border-radius-top-left: 10px;
                              --framer-input-border-radius-top-right: 10px;
                              --framer-input-font-color: var(
                                --token-743cf692-1243-473f-93be-c36de257addf,
                                rgb(255, 255, 255)
                              );
                              --framer-input-icon-mask-image: none;
                              --framer-input-placeholder-color: var(
                                --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
                                rgba(255, 255, 255, 0.65)
                              );
                            "
                            class="framer-form-text-input framer-form-input-wrapper framer-siudrk"
                          >
                            <input
                              type="email"
                              name="Email"
                              placeholder="Your email address"
                              class="framer-form-input framer-form-input-empty"
                            />
                          </div>
                        </button>
                      </div>
                    </div>
                    <div class="ssr-variant hidden-qdhrgs hidden-1wemzx2">
                      <div class="framer-18iywql-container">
                        <button
                          class="framer-sUZpT framer-hx6qqh framer-v-hx6qqh"
                          data-framer-name="Variant 1"
                          data-reset="button"
                          style="height: 100%; width: 100%"
                        >
                          <div
                            style="
                              --framer-input-background: var(
                                --token-a5fc4ef8-56b0-4925-86bf-ce9af08e7778,
                                rgb(47, 47, 47)
                              );
                              --framer-input-border-radius-bottom-left: 10px;
                              --framer-input-border-radius-bottom-right: 10px;
                              --framer-input-border-radius-top-left: 10px;
                              --framer-input-border-radius-top-right: 10px;
                              --framer-input-font-color: var(
                                --token-743cf692-1243-473f-93be-c36de257addf,
                                rgb(255, 255, 255)
                              );
                              --framer-input-icon-mask-image: none;
                              --framer-input-placeholder-color: var(
                                --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
                                rgba(255, 255, 255, 0.65)
                              );
                            "
                            class="framer-form-text-input framer-form-input-wrapper framer-siudrk"
                          >
                            <input
                              type="email"
                              name="Email"
                              placeholder="Your email address"
                              class="framer-form-input framer-form-input-empty"
                            />
                          </div>
                        </button>
                      </div></div
                  ></label>
                  <div class="ssr-variant hidden-1wemzx2 hidden-j0jgam">
                    <div class="framer-nhgtnw-container">
                      <button
                        class="framer-QI2Hr framer-133ybg9 framer-v-133ybg9"
                        data-framer-name="Default"
                        data-reset="button"
                        style="
                          background-color: var(
                            --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                            rgb(140, 255, 46)
                          );
                          border-bottom-left-radius: 10px;
                          border-bottom-right-radius: 10px;
                          border-top-left-radius: 10px;
                          border-top-right-radius: 10px;
                          box-shadow: 0px 8px 20px 0px rgba(132, 255, 31, 0.32);
                          width: 100%;
                          opacity: 1;
                        "
                        tabindex="0"
                      >
                        <div
                          class="framer-bf6m3t"
                          style="
                            outline: none;
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;
                            flex-shrink: 0;
                            --extracted-1w1cjl5: var(
                              --token-142de566-1cef-4aec-a905-86f484066d50,
                              rgb(13, 13, 13)
                            );
                            --framer-link-text-color: rgb(0, 153, 255);
                            --framer-link-text-decoration: underline;
                            transform: none;
                          "
                          data-framer-component-type="RichTextContainer"
                        >
                          <h6
                            style="
                              --font-selector: RlM7TWFucm9wZS1zZW1pYm9sZA==;
                              --framer-font-family: 'Manrope',
                                'Manrope Placeholder', sans-serif;
                              --framer-font-size: 15px;
                              --framer-font-weight: 600;
                              --framer-letter-spacing: -0.02em;
                              --framer-line-height: 1.4em;
                              --framer-text-color: var(
                                --extracted-1w1cjl5,
                                var(
                                  --token-142de566-1cef-4aec-a905-86f484066d50,
                                  rgb(13, 13, 13)
                                )
                              );
                            "
                            class="framer-text"
                          >
                            Join Now
                          </h6>
                        </div>
                      </button>
                    </div>
                  </div>
                  <div class="ssr-variant hidden-qdhrgs hidden-j0jgam">
                    <div class="framer-nhgtnw-container">
                      <button
                        class="framer-QI2Hr framer-133ybg9 framer-v-133ybg9"
                        data-framer-name="Default"
                        data-reset="button"
                        style="
                          background-color: var(
                            --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                            rgb(140, 255, 46)
                          );
                          border-bottom-left-radius: 10px;
                          border-bottom-right-radius: 10px;
                          border-top-left-radius: 10px;
                          border-top-right-radius: 10px;
                          box-shadow: 0px 8px 20px 0px rgba(132, 255, 31, 0.32);
                          width: 100%;
                          opacity: 1;
                        "
                        tabindex="0"
                      >
                        <div
                          class="framer-bf6m3t"
                          style="
                            outline: none;
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;
                            flex-shrink: 0;
                            --extracted-1w1cjl5: var(
                              --token-142de566-1cef-4aec-a905-86f484066d50,
                              rgb(13, 13, 13)
                            );
                            --framer-link-text-color: rgb(0, 153, 255);
                            --framer-link-text-decoration: underline;
                            transform: none;
                          "
                          data-framer-component-type="RichTextContainer"
                        >
                          <h6
                            style="
                              --font-selector: RlM7TWFucm9wZS1zZW1pYm9sZA==;
                              --framer-font-family: 'Manrope',
                                'Manrope Placeholder', sans-serif;
                              --framer-font-size: 15px;
                              --framer-font-weight: 600;
                              --framer-letter-spacing: -0.02em;
                              --framer-line-height: 1.4em;
                              --framer-text-color: var(
                                --extracted-1w1cjl5,
                                var(
                                  --token-142de566-1cef-4aec-a905-86f484066d50,
                                  rgb(13, 13, 13)
                                )
                              );
                            "
                            class="framer-text"
                          >
                            Join Now
                          </h6>
                        </div>
                      </button>
                    </div>
                  </div>
                  <div class="ssr-variant hidden-qdhrgs hidden-1wemzx2">
                    <div class="framer-nhgtnw-container">
                      <button
                        class="framer-QI2Hr framer-133ybg9 framer-v-133ybg9"
                        data-framer-name="Default"
                        data-reset="button"
                        style="
                          background-color: var(
                            --token-2d3de992-80f6-43cc-b5d5-16857da63015,
                            rgb(140, 255, 46)
                          );
                          border-bottom-left-radius: 10px;
                          border-bottom-right-radius: 10px;
                          border-top-left-radius: 10px;
                          border-top-right-radius: 10px;
                          box-shadow: 0px 8px 20px 0px rgba(132, 255, 31, 0.32);
                          width: 100%;
                          opacity: 1;
                        "
                        tabindex="0"
                      >
                        <div
                          class="framer-bf6m3t"
                          style="
                            outline: none;
                            display: flex;
                            flex-direction: column;
                            justify-content: flex-start;
                            flex-shrink: 0;
                            --extracted-1w1cjl5: var(
                              --token-142de566-1cef-4aec-a905-86f484066d50,
                              rgb(13, 13, 13)
                            );
                            --framer-link-text-color: rgb(0, 153, 255);
                            --framer-link-text-decoration: underline;
                            transform: none;
                          "
                          data-framer-component-type="RichTextContainer"
                        >
                          <h6
                            style="
                              --font-selector: RlM7TWFucm9wZS1zZW1pYm9sZA==;
                              --framer-font-family: 'Manrope',
                                'Manrope Placeholder', sans-serif;
                              --framer-font-size: 15px;
                              --framer-font-weight: 600;
                              --framer-letter-spacing: -0.02em;
                              --framer-line-height: 1.4em;
                              --framer-text-color: var(
                                --extracted-1w1cjl5,
                                var(
                                  --token-142de566-1cef-4aec-a905-86f484066d50,
                                  rgb(13, 13, 13)
                                )
                              );
                            "
                            class="framer-text"
                          >
                            Join Now
                          </h6>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              <div
                class="framer-1p5hx3f"
                style="
                  outline: none;
                  display: flex;
                  flex-direction: column;
                  justify-content: flex-start;
                  flex-shrink: 0;
                  transform: none;
                "
                data-framer-component-type="RichTextContainer"
              >
                <p
                  class="framer-text framer-styles-preset-cb0nmb"
                  data-styles-preset="Ro3LwE02r"
                  style="
                    --framer-text-alignment: center;
                    --framer-text-color: var(
                      --token-d2e3bc9a-15a4-4828-bd3c-44ef25339a7a,
                      rgba(255, 255, 255, 0.65)
                    );
                  "
                >
                  Limited spots available before public release.
                </p>
              </div>
              <div
                class="framer-axjy1m"
                data-framer-name="Social Media Buttons"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <div id="overlay"></div>
    </div>
    <script>
      (() => {
        function u() {
          function n(t, e, i) {
            let r = document.createElement("a");
            (r.href = t),
              (r.target = i),
              (r.rel = e),
              document.body.appendChild(r),
              r.click(),
              r.remove();
          }
          function o(t) {
            if (this.dataset.hydrated) {
              this.removeEventListener("click", o);
              return;
            }
            t.preventDefault(), t.stopPropagation();
            let e = this.getAttribute("href");
            if (!e) return;
            if (
              /Mac|iPod|iPhone|iPad/u.test(navigator.userAgent)
                ? t.metaKey
                : t.ctrlKey
            )
              return n(e, "", "_blank");
            let r = this.getAttribute("rel") ?? "",
              c = this.getAttribute("target") ?? "";
            n(e, r, c);
          }
          function a(t) {
            if (this.dataset.hydrated) {
              this.removeEventListener("auxclick", o);
              return;
            }
            t.preventDefault(), t.stopPropagation();
            let e = this.getAttribute("href");
            e && n(e, "", "_blank");
          }
          function s(t) {
            if (this.dataset.hydrated) {
              this.removeEventListener("keydown", s);
              return;
            }
            if (t.key !== "Enter") return;
            t.preventDefault(), t.stopPropagation();
            let e = this.getAttribute("href");
            if (!e) return;
            let i = this.getAttribute("rel") ?? "",
              r = this.getAttribute("target") ?? "";
            n(e, i, r);
          }
          document.querySelectorAll("[data-nested-link]").forEach((t) => {
            t instanceof HTMLElement &&
              (t.addEventListener("click", o),
              t.addEventListener("auxclick", a),
              t.addEventListener("keydown", s));
          });
        }
        return u;
      })()();
    </script>
    <script>
      (() => {
        function i() {
          for (let e of document.querySelectorAll(
            "[data-framer-original-sizes]"
          )) {
            let t = e.getAttribute("data-framer-original-sizes");
            t === "" ? e.removeAttribute("sizes") : e.setAttribute("sizes", t),
              e.removeAttribute("data-framer-original-sizes");
          }
        }
        function a() {
          window.__framer_onRewriteBreakpoints = i;
        }
        return a;
      })()();
    </script>
    <script>
      !(function () {
        function c(t, r) {
          let e = r.indexOf("#"),
            n = e === -1 ? r : r.substring(0, e),
            o = e === -1 ? "" : r.substring(e),
            a = n.indexOf("?");
          if (a === -1) return n + t + o;
          let d = new URLSearchParams(t),
            h = n.substring(a + 1),
            s = new URLSearchParams(h);
          for (let [i, m] of d) s.has(i) || s.append(i, m);
          return n.substring(0, a + 1) + s.toString() + o;
        }
        var l =
            'div#main a[href^="#"],div#main a[href^="/"],div#main a[href^="."]',
          u = "div#main a[data-framer-preserve-params]",
          f,
          g =
            (f = document.currentScript) == null
              ? void 0
              : f.hasAttribute("data-preserve-internal-params");
        if (
          window.location.search &&
          !/bot|-google|google-|yandex|ia_archiver|crawl|spider/iu.test(
            navigator.userAgent
          )
        ) {
          let t = document.querySelectorAll(g ? `${l},${u}` : u);
          for (let r of t) {
            let e = c(window.location.search, r.href);
            r.setAttribute("href", e);
          }
        }
      })();
    </script>

    <script data-framer-appear-animation="no-preference"></script>
    <link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/react.V19qq1Km.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/rolldown-runtime.B3Hfx2tR.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/motion.De8UnZCg.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/framer.DpVD1O6f.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/shared-lib.VZolBvHV.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/Ro3LwE02r.XbClR6Kt.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/AYxsxblIT.B7_gxjTk.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/y5FcLWj2c.Bm7QLgkx.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/-NQctI2t19d5ORg24tsbsvom5adQeuWh9bjXUv_Qx9k.Ch48wU4B.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/qLSEzKv0W.1SlNjb-w.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/iXqsMrUlo.BbmZI95Z.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/Np63Uxhvj.FDGYQG_T.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/SqFjj1czL.BCIHxzBE.mjs"
    /><link
      rel="modulepreload"
      fetchpriority="low"
      href="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/script_main.DD4VrJfz.mjs"
    />
    <script
      type="module"
      async
      data-framer-bundle="main"
      fetchpriority="low"
      src="https://framerusercontent.com/sites/q5XtKhLvEvLpCvibTADHW/script_main.DD4VrJfz.mjs"
    ></script>
    <div
      id="svg-templates"
      style="
        position: absolute;
        overflow: hidden;
        bottom: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 0;
        contain: strict;
      "
      aria-hidden="true"
    ></div>
  </body>
</html>
