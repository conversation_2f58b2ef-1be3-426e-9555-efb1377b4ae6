{"version": 2, "buildCommand": "", "outputDirectory": ".", "installCommand": "", "trailingSlash": false, "cleanUrls": true, "redirects": [{"source": "/blog", "destination": "/blog/getting-started"}, {"source": "/blog/", "destination": "/blog/getting-started"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/(.*)\\.(js|css|woff2|woff|ttf|eot|svg|png|jpg|jpeg|gif|ico|webp)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}