/**
 * Document processing utilities for DOCX file handling
 */

export interface DocumentProcessingResult {
  success: boolean;
  content?: string;
  title?: string;
  error?: string;
  warnings?: string[];
}

export interface ProcessingOptions {
  maxFileSize?: number;
  allowedExtensions?: string[];
  imageHandling?: 'base64' | 'url' | 'remove';
  preserveStyles?: boolean;
}

/**
 * Default processing options
 */
export const DEFAULT_OPTIONS: ProcessingOptions = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedExtensions: ['.docx', '.doc'],
  imageHandling: 'base64',
  preserveStyles: true
};

/**
 * Validates uploaded file before processing
 */
export function validateFile(file: File, options: ProcessingOptions = DEFAULT_OPTIONS): { valid: boolean; error?: string } {
  // Check file size
  if (options.maxFileSize && file.size > options.maxFileSize) {
    return {
      valid: false,
      error: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(options.maxFileSize)})`
    };
  }

  // Check file extension
  const extension = getFileExtension(file.name);
  if (options.allowedExtensions && !options.allowedExtensions.includes(extension)) {
    return {
      valid: false,
      error: `File type "${extension}" is not supported. Allowed types: ${options.allowedExtensions.join(', ')}`
    };
  }

  // Check if file is empty
  if (file.size === 0) {
    return {
      valid: false,
      error: 'File is empty'
    };
  }

  return { valid: true };
}

/**
 * Processes DOCX file and converts to HTML
 */
export async function processDocxFile(file: File, options: ProcessingOptions = DEFAULT_OPTIONS): Promise<DocumentProcessingResult> {
  try {
    // Validate file first
    const validation = validateFile(file, options);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // Dynamic import of mammoth to avoid bundling issues
    const mammoth = await import('mammoth');
    
    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Configure mammoth options
    const mammothOptions = {
      convertImage: options.imageHandling === 'base64' 
        ? mammoth.images.imgElement((image: any) => {
            return image.read("base64").then((imageBuffer: string) => {
              return {
                src: "data:" + image.contentType + ";base64," + imageBuffer
              };
            });
          })
        : options.imageHandling === 'remove'
        ? mammoth.images.imgElement(() => ({ src: '' }))
        : undefined,
      
      styleMap: options.preserveStyles ? [
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh",
        "p[style-name='Heading 3'] => h3:fresh",
        "p[style-name='Heading 4'] => h4:fresh",
        "p[style-name='Heading 5'] => h5:fresh",
        "p[style-name='Heading 6'] => h6:fresh",
        "p[style-name='Title'] => h1.title:fresh",
        "p[style-name='Subtitle'] => h2.subtitle:fresh",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em"
      ] : undefined
    };

    // Convert DOCX to HTML
    const result = await mammoth.convertToHtml({ arrayBuffer }, mammothOptions);
    
    // Extract document title from filename
    const title = file.name.replace(/\.(docx?|doc)$/i, '');
    
    // Process warnings
    const warnings = result.messages
      .filter(message => message.type === 'warning')
      .map(message => message.message);

    return {
      success: true,
      content: result.value,
      title,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    console.error('Error processing DOCX file:', error);
    
    return {
      success: false,
      error: error instanceof Error 
        ? `Failed to process document: ${error.message}`
        : 'Unknown error occurred while processing document'
    };
  }
}

/**
 * Extracts text content from DOCX file (without HTML formatting)
 */
export async function extractTextFromDocx(file: File): Promise<DocumentProcessingResult> {
  try {
    const validation = validateFile(file);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error
      };
    }

    const mammoth = await import('mammoth');
    const arrayBuffer = await file.arrayBuffer();
    
    const result = await mammoth.extractRawText({ arrayBuffer });
    
    return {
      success: true,
      content: result.value,
      title: file.name.replace(/\.(docx?|doc)$/i, '')
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to extract text from document'
    };
  }
}

/**
 * Utility functions
 */

export function getFileExtension(filename: string): string {
  return filename.toLowerCase().substring(filename.lastIndexOf('.'));
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-z0-9.-]/gi, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '');
}

/**
 * Error types for better error handling
 */
export enum DocumentProcessingError {
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  EMPTY_FILE = 'EMPTY_FILE',
  PARSING_ERROR = 'PARSING_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Custom error class for document processing
 */
export class DocumentError extends Error {
  constructor(
    public type: DocumentProcessingError,
    message: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'DocumentError';
  }
}

/**
 * Progress callback type for file processing
 */
export type ProgressCallback = (progress: number, stage: string) => void;

/**
 * Advanced processing with progress tracking
 */
export async function processDocxWithProgress(
  file: File, 
  options: ProcessingOptions = DEFAULT_OPTIONS,
  onProgress?: ProgressCallback
): Promise<DocumentProcessingResult> {
  try {
    onProgress?.(0, 'Validating file...');
    
    const validation = validateFile(file, options);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }
    
    onProgress?.(20, 'Loading document processor...');
    const mammoth = await import('mammoth');
    
    onProgress?.(40, 'Reading file...');
    const arrayBuffer = await file.arrayBuffer();
    
    onProgress?.(60, 'Converting document...');
    const result = await mammoth.convertToHtml({ arrayBuffer });
    
    onProgress?.(80, 'Processing content...');
    const title = file.name.replace(/\.(docx?|doc)$/i, '');
    
    onProgress?.(100, 'Complete');
    
    return {
      success: true,
      content: result.value,
      title,
      warnings: result.messages.map(m => m.message)
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Processing failed'
    };
  }
}
