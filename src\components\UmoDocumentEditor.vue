<template>
  <div class="umo-document-editor">
    <!-- File Upload Section -->
    <div class="upload-section" v-if="!documentLoaded">
      <FileUploadZone
        title="Upload DOCX Document"
        description="Drag and drop your DOCX file here, or click to browse"
        :accepted-types="['.docx', '.doc']"
        :max-file-size="10 * 1024 * 1024"
        @file-selected="handleFileSelected"
        @file-processed="handleFileProcessed"
        @error="handleUploadError"
        @processing-start="handleProcessingStart"
        @processing-end="handleProcessingEnd"
      />
    </div>

    <!-- UMO Editor Section -->
    <div v-if="documentLoaded && !loading" class="editor-section">
      <div class="editor-header">
        <h2>{{ documentTitle || 'Untitled Document' }}</h2>
        <div class="editor-actions">
          <button @click="saveDocument" class="save-btn">Save</button>
          <button @click="exportDocument" class="export-btn">Export</button>
          <button @click="resetUpload" class="new-btn">New Document</button>
        </div>
      </div>

      <!-- UMO Editor Container -->
      <div class="editor-container">
        <umo-editor
          ref="umoEditor"
          v-bind="editorOptions"
          @save="handleSave"
          @change="handleChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { UmoEditor } from '@umoteam/editor'
import FileUploadZone from './FileUploadZone.vue'

// Component state
const documentLoaded = ref(false)
const loading = ref(false)
const error = ref('')
const documentTitle = ref('')
const documentContent = ref('')
const umoEditor = ref()
const processingWarnings = ref<string[]>([])
const selectedFile = ref<File | null>(null)

// UMO Editor configuration
const editorOptions = reactive({
  editorKey: 'docgen-editor',
  locale: 'en-US',
  theme: 'light',
  height: '600px',
  toolbar: {
    defaultMode: 'ribbon',
    menus: ['base', 'insert', 'table', 'tools', 'page', 'export'],
    importWord: {
      enabled: true,
      maxSize: 1024 * 1024 * 10, // 10MB
      options: {
        // Mammoth.js options for better DOCX parsing
        convertImage: (image: any) => {
          return image.read("base64").then((imageBuffer: string) => {
            return {
              src: "data:" + image.contentType + ";base64," + imageBuffer
            }
          })
        }
      },
      useCustomMethod: true,
      onCustomImportMethod: handleCustomImport
    }
  },
  document: {
    title: '',
    content: '',
    placeholder: 'Start typing your document content...',
    enableSpellcheck: true,
    enableMarkdown: true,
    enableBubbleMenu: true,
    enableBlockMenu: true,
    readOnly: false,
    autofocus: true
  },
  page: {
    defaultMargin: {
      left: 2.54,
      right: 2.54,
      top: 2.54,
      bottom: 2.54
    },
    defaultOrientation: 'portrait',
    defaultBackground: '#fff',
    showBreakMarks: true
  },
  onSave: handleSave,
  onFileUpload: handleFileUpload
})

// File handling methods - Updated to work with FileUploadZone
const handleFileSelected = (file: File) => {
  selectedFile.value = file
  console.log('File selected:', file.name)
}

const handleFileProcessed = (result: { content: string; title: string; warnings?: string[] }) => {
  documentContent.value = result.content
  documentTitle.value = result.title
  processingWarnings.value = result.warnings || []

  // Update editor options
  editorOptions.document.content = result.content
  editorOptions.document.title = result.title

  // Mark document as loaded
  documentLoaded.value = true
  loading.value = false

  // Focus the editor after loading
  setTimeout(() => {
    if (umoEditor.value) {
      umoEditor.value.focus()
    }
  }, 100)

  // Show warnings if any
  if (processingWarnings.value.length > 0) {
    console.warn('Document processing warnings:', processingWarnings.value)
    // You could show a toast notification here
  }
}

const handleUploadError = (errorMessage: string) => {
  error.value = errorMessage
  loading.value = false
  console.error('Upload error:', errorMessage)
}

const handleProcessingStart = () => {
  loading.value = true
  error.value = ''
}

const handleProcessingEnd = () => {
  loading.value = false
}

// Custom import method for UMO Editor (if needed)
async function handleCustomImport(file: File) {
  // This method is called by UMO Editor's built-in import functionality
  // We're now using FileUploadZone for file processing, but keeping this
  // for compatibility with UMO Editor's import feature
  try {
    const mammoth = await import('mammoth')
    const arrayBuffer = await file.arrayBuffer()
    const result = await mammoth.convertToHtml({ arrayBuffer })

    return {
      content: result.value,
      title: file.name.replace(/\.(docx?|doc)$/i, ''),
      warnings: result.messages.map(m => m.message)
    }
  } catch (err) {
    throw new Error('Failed to parse DOCX file. Please ensure it\'s a valid document.')
  }
}

// Editor event handlers
const handleSave = async (content: string, page: any, document: any) => {
  try {
    // Here you would typically save to your backend
    console.log('Saving document:', { content, page, document })

    // For now, just update local state
    documentContent.value = content

    // Show success message (you can implement a toast notification)
    alert('Document saved successfully!')
  } catch (err) {
    console.error('Save failed:', err)
    alert('Failed to save document.')
  }
}

const handleChange = (content: string) => {
  documentContent.value = content
}

const handleFileUpload = async (file: File) => {
  // Handle file uploads within the editor (images, etc.)
  try {
    // Convert file to base64 for demo purposes
    const reader = new FileReader()
    return new Promise((resolve, reject) => {
      reader.onload = () => {
        resolve({
          url: reader.result as string,
          name: file.name,
          size: file.size
        })
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  } catch (err) {
    throw new Error('Failed to upload file.')
  }
}

// Utility methods
const saveDocument = () => {
  if (umoEditor.value) {
    umoEditor.value.saveContent(true)
  }
}

const exportDocument = () => {
  if (umoEditor.value) {
    // Get document content in different formats
    const html = umoEditor.value.getHTML()
    const text = umoEditor.value.getText()

    // For demo, download as HTML
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${documentTitle.value || 'document'}.html`
    a.click()
    URL.revokeObjectURL(url)
  }
}

const resetUpload = () => {
  documentLoaded.value = false
  loading.value = false
  error.value = ''
  documentTitle.value = ''
  documentContent.value = ''
  processingWarnings.value = []
  selectedFile.value = null
  editorOptions.document.content = ''
  editorOptions.document.title = ''
}

onMounted(() => {
  // Any initialization logic
  console.log('UMO Document Editor mounted')
})
</script>

<style scoped>
.umo-document-editor {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.upload-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.save-btn, .export-btn, .new-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.save-btn:hover {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.export-btn:hover {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.new-btn:hover {
  background: #6b7280;
  color: white;
  border-color: #6b7280;
}

.editor-container {
  flex: 1;
  background: white;
}
</style>
