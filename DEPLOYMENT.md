# Vercel Deployment Guide for DocGen

This guide will walk you through deploying your DocGen project to Vercel.

## 🚀 Quick Deployment (Recommended)

### Option 1: Deploy via Vercel Dashboard

1. **Push your code to GitHub**
   ```bash
   git add .
   git commit -m "Prepare for Vercel deployment"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with your GitHub account
   - Click "New Project"
   - Import your DocGen repository

3. **Configure Project**
   - Project Name: `docgen` (or your preferred name)
   - Framework Preset: `Other`
   - Root Directory: `./` (leave as default)
   - Build Command: Leave empty (static site)
   - Output Directory: Leave empty (root directory)
   - Install Command: `npm install` (optional)

4. **Deploy**
   - Click "Deploy"
   - Wait for deployment to complete
   - Your site will be live at `https://your-project-name.vercel.app`

### Option 2: Deploy via Vercel CLI

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel
   ```
   
   Follow the prompts:
   - Set up and deploy? `Y`
   - Which scope? Select your account
   - Link to existing project? `N`
   - Project name: `docgen`
   - In which directory is your code located? `./`

4. **Production Deployment**
   ```bash
   vercel --prod
   ```

## 📁 Project Structure (Vercel-Ready)

```
DocGen/
├── index.html              # Main page (was page.html)
├── contact/
│   └── index.html          # Contact page (was page.html)
├── waitlist/
│   └── index.html          # Waitlist page (was page.html)
├── blog/
│   ├── getting-started/
│   │   └── index.html      # Blog post (was page.html)
│   ├── building-trust/
│   │   └── index.html      # Blog post (was page.html)
│   ├── early-user-feedback/
│   │   └── index.html      # Blog post (was page.html)
│   └── landing-page-design/
│       └── index.html      # Blog post (was page.html)
├── vercel.json             # Vercel configuration
├── package.json            # Project metadata
├── README.md               # Documentation
├── DEPLOYMENT.md           # This file
├── .gitignore              # Git ignore rules
└── LICENSE                 # MIT License
```

## ⚙️ Configuration Files

### vercel.json
- Handles clean URLs (removes .html extensions)
- Sets up proper routing for all pages
- Configures security headers
- Optimizes caching for static assets

### package.json
- Contains project metadata
- Defines scripts for local development
- Specifies Node.js version requirements

## 🔧 Local Testing

Before deploying, test locally:

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start local server**
   ```bash
   npm run dev
   ```

3. **Test all routes**
   - Main page: `http://localhost:3000`
   - Contact: `http://localhost:3000/contact`
   - Waitlist: `http://localhost:3000/waitlist`
   - Blog posts: `http://localhost:3000/blog/getting-started`

## 🌐 Custom Domain (Optional)

1. **In Vercel Dashboard**
   - Go to your project settings
   - Click "Domains"
   - Add your custom domain
   - Follow DNS configuration instructions

2. **DNS Configuration**
   - Add CNAME record pointing to `cname.vercel-dns.com`
   - Or add A record pointing to Vercel's IP

## 🔍 Troubleshooting

### Common Issues

1. **404 Errors**
   - Ensure all `page.html` files are renamed to `index.html`
   - Check `vercel.json` routing configuration

2. **Assets Not Loading**
   - Framer assets are served from CDN (should work automatically)
   - Check browser console for any blocked resources

3. **Routing Issues**
   - Verify `vercel.json` routes match your directory structure
   - Test routes locally before deploying

### Deployment Logs

Check deployment logs in Vercel dashboard:
- Go to your project
- Click on a deployment
- View "Function Logs" and "Build Logs"

## 📊 Performance Optimization

The project is already optimized for Vercel:
- ✅ Static HTML files
- ✅ CDN-served assets
- ✅ Proper caching headers
- ✅ Compressed resources
- ✅ Responsive design

## 🔒 Security

Security headers are configured in `vercel.json`:
- Content Security Policy
- XSS Protection
- Frame Options
- Referrer Policy

## 📈 Analytics (Optional)

Add Vercel Analytics:
1. Go to project settings in Vercel
2. Enable "Analytics"
3. View traffic and performance metrics

## 🆘 Support

If you encounter issues:
1. Check Vercel documentation: [vercel.com/docs](https://vercel.com/docs)
2. Review deployment logs in Vercel dashboard
3. Test locally first with `npm run dev`
4. Check GitHub repository for updates

---

🎉 **Congratulations!** Your DocGen site should now be live on Vercel!
