import { describe, it, expect, beforeEach, vi } from 'vitest'
import { 
  validateFile, 
  processDocxFile, 
  extractTextFromDocx,
  formatFileSize,
  getFileExtension,
  sanitizeFilename,
  DocumentProcessingError,
  DocumentError,
  DEFAULT_OPTIONS
} from '../utils/documentProcessor'

// Mock mammoth.js
vi.mock('mammoth', () => ({
  default: {
    convertToHtml: vi.fn(),
    extractRawText: vi.fn(),
    images: {
      imgElement: vi.fn()
    }
  }
}))

describe('DocumentProcessor', () => {
  let mockFile: File

  beforeEach(() => {
    // Create a mock file for testing
    mockFile = new File(['test content'], 'test-document.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
  })

  describe('validateFile', () => {
    it('should validate a valid DOCX file', () => {
      const result = validateFile(mockFile)
      expect(result.valid).toBe(true)
      expect(result.error).toBeUndefined()
    })

    it('should reject files that are too large', () => {
      const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.docx', {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      
      const result = validateFile(largeFile)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('exceeds maximum allowed size')
    })

    it('should reject unsupported file types', () => {
      const txtFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      
      const result = validateFile(txtFile)
      expect(result.valid).toBe(false)
      expect(result.error).toContain('not supported')
    })

    it('should reject empty files', () => {
      const emptyFile = new File([], 'empty.docx', {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      
      const result = validateFile(emptyFile)
      expect(result.valid).toBe(false)
      expect(result.error).toBe('File is empty')
    })

    it('should use custom options', () => {
      const customOptions = {
        maxFileSize: 1024, // 1KB
        allowedExtensions: ['.pdf']
      }
      
      const result = validateFile(mockFile, customOptions)
      expect(result.valid).toBe(false)
    })
  })

  describe('processDocxFile', () => {
    it('should process a valid DOCX file successfully', async () => {
      // Mock mammoth response
      const mockMammoth = await import('mammoth')
      vi.mocked(mockMammoth.convertToHtml).mockResolvedValue({
        value: '<p>Test content</p>',
        messages: []
      })

      const result = await processDocxFile(mockFile)
      
      expect(result.success).toBe(true)
      expect(result.content).toBe('<p>Test content</p>')
      expect(result.title).toBe('test-document')
      expect(result.warnings).toBeUndefined()
    })

    it('should handle mammoth conversion warnings', async () => {
      const mockMammoth = await import('mammoth')
      vi.mocked(mockMammoth.convertToHtml).mockResolvedValue({
        value: '<p>Test content</p>',
        messages: [
          { type: 'warning', message: 'Unknown style encountered' }
        ]
      })

      const result = await processDocxFile(mockFile)
      
      expect(result.success).toBe(true)
      expect(result.warnings).toEqual(['Unknown style encountered'])
    })

    it('should handle processing errors', async () => {
      const mockMammoth = await import('mammoth')
      vi.mocked(mockMammoth.convertToHtml).mockRejectedValue(
        new Error('Invalid DOCX format')
      )

      const result = await processDocxFile(mockFile)
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('Failed to process document')
    })

    it('should validate file before processing', async () => {
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      
      const result = await processDocxFile(invalidFile)
      
      expect(result.success).toBe(false)
      expect(result.error).toContain('not supported')
    })
  })

  describe('extractTextFromDocx', () => {
    it('should extract plain text from DOCX', async () => {
      const mockMammoth = await import('mammoth')
      vi.mocked(mockMammoth.extractRawText).mockResolvedValue({
        value: 'Plain text content'
      })

      const result = await extractTextFromDocx(mockFile)
      
      expect(result.success).toBe(true)
      expect(result.content).toBe('Plain text content')
      expect(result.title).toBe('test-document')
    })

    it('should handle text extraction errors', async () => {
      const mockMammoth = await import('mammoth')
      vi.mocked(mockMammoth.extractRawText).mockRejectedValue(
        new Error('Extraction failed')
      )

      const result = await extractTextFromDocx(mockFile)
      
      expect(result.success).toBe(false)
      expect(result.error).toBe('Extraction failed')
    })
  })

  describe('Utility functions', () => {
    describe('formatFileSize', () => {
      it('should format bytes correctly', () => {
        expect(formatFileSize(0)).toBe('0 Bytes')
        expect(formatFileSize(1024)).toBe('1 KB')
        expect(formatFileSize(1024 * 1024)).toBe('1 MB')
        expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
      })

      it('should handle decimal places', () => {
        expect(formatFileSize(1536)).toBe('1.5 KB')
        expect(formatFileSize(1024 * 1024 * 1.5)).toBe('1.5 MB')
      })
    })

    describe('getFileExtension', () => {
      it('should extract file extensions correctly', () => {
        expect(getFileExtension('document.docx')).toBe('.docx')
        expect(getFileExtension('file.DOC')).toBe('.doc')
        expect(getFileExtension('no-extension')).toBe('')
        expect(getFileExtension('multiple.dots.docx')).toBe('.docx')
      })
    })

    describe('sanitizeFilename', () => {
      it('should sanitize filenames', () => {
        expect(sanitizeFilename('normal-file.docx')).toBe('normal-file.docx')
        expect(sanitizeFilename('file with spaces.docx')).toBe('file_with_spaces.docx')
        expect(sanitizeFilename('file@#$%^&*().docx')).toBe('file_.docx')
        expect(sanitizeFilename('___multiple___underscores___')).toBe('multiple_underscores')
      })
    })
  })

  describe('DocumentError', () => {
    it('should create custom error with type', () => {
      const error = new DocumentError(
        DocumentProcessingError.INVALID_FILE_TYPE,
        'Invalid file type',
        new Error('Original error')
      )

      expect(error.name).toBe('DocumentError')
      expect(error.type).toBe(DocumentProcessingError.INVALID_FILE_TYPE)
      expect(error.message).toBe('Invalid file type')
      expect(error.originalError).toBeInstanceOf(Error)
    })
  })

  describe('DEFAULT_OPTIONS', () => {
    it('should have correct default values', () => {
      expect(DEFAULT_OPTIONS.maxFileSize).toBe(10 * 1024 * 1024)
      expect(DEFAULT_OPTIONS.allowedExtensions).toEqual(['.docx', '.doc'])
      expect(DEFAULT_OPTIONS.imageHandling).toBe('base64')
      expect(DEFAULT_OPTIONS.preserveStyles).toBe(true)
    })
  })
})

// Integration tests
describe('DocumentProcessor Integration', () => {
  it('should handle the complete file processing workflow', async () => {
    const mockFile = new File(['test content'], 'integration-test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })

    // Mock mammoth
    const mockMammoth = await import('mammoth')
    vi.mocked(mockMammoth.convertToHtml).mockResolvedValue({
      value: '<h1>Title</h1><p>Content</p>',
      messages: [
        { type: 'warning', message: 'Style not found' }
      ]
    })

    const result = await processDocxFile(mockFile, {
      maxFileSize: 20 * 1024 * 1024,
      allowedExtensions: ['.docx'],
      imageHandling: 'base64',
      preserveStyles: true
    })

    expect(result.success).toBe(true)
    expect(result.content).toBe('<h1>Title</h1><p>Content</p>')
    expect(result.title).toBe('integration-test')
    expect(result.warnings).toEqual(['Style not found'])
  })

  it('should handle network errors gracefully', async () => {
    const mockFile = new File(['test'], 'network-test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })

    // Mock network error
    vi.doMock('mammoth', () => {
      throw new Error('Network error')
    })

    const result = await processDocxFile(mockFile)
    
    expect(result.success).toBe(false)
    expect(result.error).toContain('Failed to process document')
  })
})
