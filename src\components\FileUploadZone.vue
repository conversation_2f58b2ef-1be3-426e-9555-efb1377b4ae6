<template>
  <div class="file-upload-zone">
    <div 
      class="drop-zone"
      :class="{ 
        'drag-over': isDragOver,
        'has-error': error,
        'is-processing': isProcessing
      }"
      @drop="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <input
        ref="fileInput"
        type="file"
        :accept="acceptedTypes.join(',')"
        @change="handleFileSelect"
        style="display: none"
      />
      
      <!-- Upload State -->
      <div v-if="!isProcessing && !error" class="upload-state">
        <div class="upload-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
            <polyline points="14,2 14,8 20,8"/>
            <line x1="16" y1="13" x2="8" y2="13"/>
            <line x1="16" y1="17" x2="8" y2="17"/>
            <polyline points="10,9 9,9 8,9"/>
          </svg>
        </div>
        <h3>{{ title }}</h3>
        <p>{{ description }}</p>
        <div class="file-info">
          <span>Supported formats: {{ acceptedTypes.join(', ') }}</span>
          <span>Maximum size: {{ formatFileSize(maxFileSize) }}</span>
        </div>
        <button class="browse-button" type="button">
          Browse Files
        </button>
      </div>
      
      <!-- Processing State -->
      <div v-if="isProcessing" class="processing-state">
        <div class="spinner">
          <div class="spinner-ring"></div>
        </div>
        <h3>Processing Document</h3>
        <p>{{ processingStage }}</p>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${processingProgress}%` }"></div>
        </div>
        <span class="progress-text">{{ processingProgress }}%</span>
      </div>
      
      <!-- Error State -->
      <div v-if="error" class="error-state">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        </div>
        <h3>Upload Failed</h3>
        <p>{{ error }}</p>
        <div class="error-actions">
          <button @click="clearError" class="retry-button">
            Try Again
          </button>
        </div>
      </div>
    </div>
    
    <!-- File Preview -->
    <div v-if="selectedFile && !error" class="file-preview">
      <div class="file-item">
        <div class="file-icon">📄</div>
        <div class="file-details">
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
        </div>
        <button @click="removeFile" class="remove-button" type="button">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { processDocxWithProgress, validateFile, formatFileSize, type ProcessingOptions, type ProgressCallback } from '../utils/documentProcessor'

// Props
interface Props {
  title?: string
  description?: string
  acceptedTypes?: string[]
  maxFileSize?: number
  multiple?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Upload DOCX Document',
  description: 'Drag and drop your document here, or click to browse',
  acceptedTypes: () => ['.docx', '.doc'],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  multiple: false,
  disabled: false
})

// Emits
interface Emits {
  fileSelected: [file: File]
  fileProcessed: [result: { content: string; title: string; warnings?: string[] }]
  error: [error: string]
  processingStart: []
  processingEnd: []
}

const emit = defineEmits<Emits>()

// Reactive state
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const isDragOver = ref(false)
const isProcessing = ref(false)
const processingProgress = ref(0)
const processingStage = ref('')
const error = ref('')

// Computed
const processingOptions = computed<ProcessingOptions>(() => ({
  maxFileSize: props.maxFileSize,
  allowedExtensions: props.acceptedTypes,
  imageHandling: 'base64',
  preserveStyles: true
}))

// Methods
const triggerFileInput = () => {
  if (props.disabled || isProcessing.value) return
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processSelectedFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  if (props.disabled || isProcessing.value) return
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processSelectedFile(files[0])
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (!props.disabled && !isProcessing.value) {
    isDragOver.value = true
  }
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  if (!props.disabled && !isProcessing.value) {
    isDragOver.value = true
  }
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  // Only set to false if we're leaving the drop zone entirely
  if (!event.currentTarget?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false
  }
}

const processSelectedFile = async (file: File) => {
  // Clear previous state
  error.value = ''
  selectedFile.value = file
  
  // Validate file
  const validation = validateFile(file, processingOptions.value)
  if (!validation.valid) {
    error.value = validation.error || 'Invalid file'
    return
  }
  
  // Emit file selected
  emit('fileSelected', file)
  
  // Start processing
  isProcessing.value = true
  processingProgress.value = 0
  processingStage.value = 'Starting...'
  emit('processingStart')
  
  try {
    // Progress callback
    const onProgress: ProgressCallback = (progress, stage) => {
      processingProgress.value = progress
      processingStage.value = stage
    }
    
    // Process the file
    const result = await processDocxWithProgress(file, processingOptions.value, onProgress)
    
    if (result.success && result.content && result.title) {
      emit('fileProcessed', {
        content: result.content,
        title: result.title,
        warnings: result.warnings
      })
    } else {
      error.value = result.error || 'Failed to process document'
    }
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
    emit('error', error.value)
  } finally {
    isProcessing.value = false
    emit('processingEnd')
  }
}

const removeFile = () => {
  selectedFile.value = null
  error.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const clearError = () => {
  error.value = ''
  removeFile()
}

// Expose methods for parent component
defineExpose({
  triggerFileInput,
  removeFile,
  clearError
})
</script>

<style scoped>
.file-upload-zone {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  background: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone:hover:not(.is-processing) {
  border-color: #3b82f6;
  background: #eff6ff;
}

.drop-zone.drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.drop-zone.has-error {
  border-color: #ef4444;
  background: #fef2f2;
}

.drop-zone.is-processing {
  cursor: not-allowed;
  opacity: 0.8;
}

.upload-state, .processing-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.upload-icon, .error-icon {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.drop-zone.has-error .error-icon {
  color: #ef4444;
}

.upload-state h3, .processing-state h3, .error-state h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.upload-state p, .processing-state p, .error-state p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #9ca3af;
}

.browse-button, .retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.browse-button:hover, .retry-button:hover {
  background: #2563eb;
}

.retry-button {
  background: #ef4444;
}

.retry-button:hover {
  background: #dc2626;
}

.spinner {
  position: relative;
  width: 48px;
  height: 48px;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  width: 200px;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.file-preview {
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-icon {
  font-size: 1.5rem;
}

.file-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
}

.file-size {
  font-size: 0.875rem;
  color: #6b7280;
}

.remove-button {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s;
}

.remove-button:hover {
  background: #f3f4f6;
  color: #ef4444;
}
</style>
